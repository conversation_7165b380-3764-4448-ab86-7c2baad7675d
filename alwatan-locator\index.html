<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="نقطة قريبة">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#3B82F6">
    <title>نقطة قريبة - ليبيا 🇱🇾</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    screens: {
                        'xs': '475px',
                    }
                }
            }
        }
    </script>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dOWTgaJzuU17R8&libraries=places"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

        :root {
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-200: #bae6fd;
            --primary-300: #7dd3fc;
            --primary-400: #38bdf8;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-800: #075985;
            --primary-900: #0c4a6e;

            --libya-red: #e11d48;
            --libya-green: #16a34a;
            --libya-black: #1f2937;

            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --shadow-soft: 0 8px 32px rgba(0, 0, 0, 0.1);
            --shadow-medium: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --shadow-large: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-800) 100%);
            --gradient-libya: linear-gradient(135deg, var(--libya-red) 0%, var(--libya-green) 50%, var(--libya-black) 100%);
            --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            touch-action: manipulation;
            background: #ffffff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(168, 85, 247, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(34, 197, 94, 0.03) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        /* شريط التنقل العصري */
        .navbar-glass {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            color: #374151;
        }

        .nav-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-button:hover::before {
            left: 100%;
        }

        .nav-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
            border-color: rgba(255, 255, 255, 0.3);
        }

        /* بطاقات محسنة للخلفية البيضاء */
        .glass-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.1),
                0 1px 0 rgba(255, 255, 255, 0.8);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(168, 85, 247, 0.03) 100%);
            border-radius: inherit;
            pointer-events: none;
        }

        .glass-card:hover {
            transform: translateY(-8px);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.15),
                0 1px 0 rgba(255, 255, 255, 1);
            border-color: rgba(59, 130, 246, 0.3);
            background: rgba(255, 255, 255, 1);
        }

        /* بطاقات الوكلاء المحسنة */
        .agent-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.12),
                0 1px 0 rgba(255, 255, 255, 0.9);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .agent-card::before {
            content: '';
            position: absolute;
            inset: 0;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(168, 85, 247, 0.02) 100%);
            border-radius: inherit;
            pointer-events: none;
        }

        .agent-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.18),
                0 1px 0 rgba(255, 255, 255, 1);
            border-color: rgba(59, 130, 246, 0.4);
            background: rgba(255, 255, 255, 1);
        }

        /* تأثيرات الحركة */
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .animate-glow {
            animation: glow 2s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.5); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
        }

        /* تحسينات للهواتف */
        @media (max-width: 768px) {
            body {
                font-size: 14px;
            }
            .container {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }
            .glass-card {
                border-radius: 16px;
            }
        }

        /* تحسينات للأيباد */
        @media (min-width: 768px) and (max-width: 1024px) {
            .container {
                padding-left: 2rem !important;
                padding-right: 2rem !important;
            }
        }

        .animate-bounce {
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
            40%, 43% { transform: translateY(-30px); }
            70% { transform: translateY(-15px); }
            90% { transform: translateY(-4px); }
        }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }

        .map-container {
            height: 300px;
            border-radius: 12px;
            overflow: hidden;
        }

        /* تحسين الخريطة للهواتف */
        @media (min-width: 640px) {
            .map-container {
                height: 400px;
            }
        }

        @media (min-width: 1024px) {
            .map-container {
                height: 500px;
            }
        }

        /* أزرار عصرية */
        .modern-button {
            min-height: 48px;
            min-width: 48px;
            touch-action: manipulation;
            background: var(--gradient-primary);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            font-size: 14px;
            padding: 12px 24px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
        }

        .modern-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .modern-button:hover::before {
            left: 100%;
        }

        .modern-button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .modern-button:active {
            transform: translateY(0);
        }

        /* أزرار ثانوية */
        .secondary-button {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: var(--libya-black);
        }

        /* أزرار الخطر */
        .danger-button {
            background: linear-gradient(135deg, var(--libya-red) 0%, #dc2626 100%);
        }

        /* أزرار النجاح */
        .success-button {
            background: linear-gradient(135deg, var(--libya-green) 0%, #059669 100%);
        }

        /* تحسين النصوص للشاشات الصغيرة */
        .responsive-text {
            font-size: clamp(0.875rem, 2.5vw, 1.125rem);
        }

        /* عناوين متدرجة */
        .gradient-text {
            background: var(--gradient-libya);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
        }

        /* نصوص محسنة للخلفية البيضاء */
        .text-white-enhanced {
            color: #1f2937 !important;
            text-shadow: none !important;
            font-weight: 700 !important;
        }

        .text-white-soft {
            color: #374151 !important;
            text-shadow: none !important;
            font-weight: 600 !important;
        }

        .text-white-muted {
            color: #6b7280 !important;
            text-shadow: none !important;
            font-weight: 500 !important;
        }

        /* خلفيات نصوص للخلفية البيضاء */
        .text-bg-dark {
            background: rgba(59, 130, 246, 0.1) !important;
            padding: 4px 12px !important;
            border-radius: 8px !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(59, 130, 246, 0.2) !important;
        }

        .text-bg-light {
            background: rgba(168, 85, 247, 0.08) !important;
            padding: 4px 12px !important;
            border-radius: 8px !important;
            backdrop-filter: blur(8px) !important;
            border: 1px solid rgba(168, 85, 247, 0.15) !important;
        }

        /* عناوين الوكلاء */
        .agent-title {
            color: #1f2937 !important;
            text-shadow: none !important;
            font-weight: 800 !important;
            background: rgba(59, 130, 246, 0.1) !important;
            padding: 6px 12px !important;
            border-radius: 10px !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(59, 130, 246, 0.2) !important;
        }

        /* عناوين الأماكن */
        .agent-location {
            color: #374151 !important;
            text-shadow: none !important;
            font-weight: 600 !important;
            background: rgba(168, 85, 247, 0.08) !important;
            padding: 4px 10px !important;
            border-radius: 8px !important;
            backdrop-filter: blur(8px) !important;
            border: 1px solid rgba(168, 85, 247, 0.15) !important;
        }

        /* بطاقة المكتب الرئيسي المميزة */
        .featured-office-card {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(168, 85, 247, 0.05) 100%);
            border: 2px solid rgba(59, 130, 246, 0.3);
            border-radius: 24px;
            box-shadow:
                0 20px 40px rgba(59, 130, 246, 0.15),
                0 8px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .featured-office-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #e11d48, #16a34a, #1f2937);
            border-radius: 24px 24px 0 0;
        }

        .featured-office-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow:
                0 30px 60px rgba(59, 130, 246, 0.2),
                0 12px 24px rgba(0, 0, 0, 0.15);
            border-color: rgba(59, 130, 246, 0.5);
        }

        /* شارة المكتب الرئيسي */
        .main-office-badge {
            background: linear-gradient(135deg, #e11d48 0%, #dc2626 100%);
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 4px 12px rgba(225, 29, 72, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* منطقة الصورة */
        .office-image-container {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-radius: 16px;
            border: 2px dashed rgba(59, 130, 246, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: default;
        }

        /* منطقة الصورة للمدير فقط */
        .office-image-container.admin-mode {
            cursor: pointer;
        }

        .office-image-container.admin-mode:hover {
            border-color: rgba(59, 130, 246, 0.5);
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        }

        .office-image-container:hover {
            border-color: rgba(59, 130, 246, 0.5);
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        }

        .office-image-container.has-image {
            border-style: solid;
            border-color: rgba(34, 197, 94, 0.3);
        }

        .office-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 14px;
        }

        .image-upload-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 14px;
        }

        .office-image-container:hover .image-upload-overlay {
            opacity: 1;
        }

        /* أيقونة المكتب الرئيسي */
        .main-office-icon {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            box-shadow:
                0 8px 24px rgba(251, 191, 36, 0.3),
                0 0 0 4px rgba(251, 191, 36, 0.1);
            animation: glow 3s ease-in-out infinite alternate;
        }

        @keyframes glow {
            from { box-shadow: 0 8px 24px rgba(251, 191, 36, 0.3), 0 0 0 4px rgba(251, 191, 36, 0.1); }
            to { box-shadow: 0 8px 32px rgba(251, 191, 36, 0.5), 0 0 0 6px rgba(251, 191, 36, 0.2); }
        }

        /* إخفاء شريط التمرير في الهواتف */
        .hide-scrollbar {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
        .hide-scrollbar::-webkit-scrollbar {
            display: none;
        }

        /* تأثيرات الضوء */
        .neon-glow {
            box-shadow:
                0 0 5px var(--primary-400),
                0 0 10px var(--primary-400),
                0 0 15px var(--primary-400),
                0 0 20px var(--primary-400);
        }

        /* تحسين الظلال للشاشات عالية الدقة */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .shadow-lg {
                box-shadow: var(--shadow-large);
            }
        }

        /* أنماط نافذة تسجيل الدخول */
        #loginModal {
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
        }

        /* تحسين حقول الإدخال */
        input:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        /* تحسين الأزرار للمس */
        .touch-button:active {
            transform: scale(0.98);
        }

        /* تحسين النصوص للشاشات الصغيرة */
        @media (max-width: 480px) {
            #loginModal .bg-white {
                margin: 1rem;
                max-height: 90vh;
                overflow-y: auto;
            }
        }

        /* تحسين الرسوم المتحركة */
        .transition-all {
            transition: all 0.3s ease;
        }

        /* تحسين المدخلات للغة العربية */
        input[type="tel"], input[type="password"] {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* تحسين الأيقونات */
        svg {
            flex-shrink: 0;
        }

        /* أنماط لوحة التحكم */
        .dashboard-nav-btn.active {
            background-color: #dbeafe;
            color: #1d4ed8;
            font-weight: 600;
        }

        .dashboard-section {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تحسين الجداول */
        table {
            font-size: 14px;
        }

        @media (max-width: 768px) {
            table {
                font-size: 12px;
            }

            .px-6 {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }
        }

        /* تحسين الأزرار في الجداول */
        table button {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s;
        }

        table button:hover {
            transform: scale(1.05);
        }

        /* تحسين البطاقات */
        .bg-white {
            transition: box-shadow 0.3s ease;
        }

        .bg-white:hover {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        }

        /* تحسين الإحصائيات */
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        /* تحسين النصوص */
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* أنماط النوافذ المنبثقة */
        .max-h-90vh {
            max-height: 90vh;
        }

        /* تحسين النماذج */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        /* تحسين الحقول */
        input:focus, select:focus, textarea:focus {
            outline: none;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            border-color: #3B82F6;
        }

        /* تحسين الأزرار */
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        button:disabled:hover {
            transform: none;
        }

        /* تحسين الإشعارات */
        .notification-enter {
            animation: slideInRight 0.3s ease-out;
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* تحسين النوافذ للهواتف */
        @media (max-width: 640px) {
            .max-w-2xl {
                max-width: 95vw;
            }

            .grid-cols-1.md\\:grid-cols-2 {
                grid-template-columns: 1fr;
            }

            .md\\:col-span-2 {
                grid-column: span 1;
            }
        }

        /* تحسين الجداول للهواتف */
        @media (max-width: 768px) {
            .overflow-x-auto {
                -webkit-overflow-scrolling: touch;
            }

            table {
                min-width: 600px;
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- شريط التنقل العصري -->
    <nav class="lg:hidden navbar-glass sticky top-0 z-50">
        <div class="flex items-center justify-between px-6 py-4">
            <div class="flex items-center space-x-3 space-x-reverse">
                <div class="w-10 h-10 rounded-full bg-gradient-to-r from-red-500 via-green-500 to-black flex items-center justify-center animate-glow">
                    <span class="text-white text-lg font-bold">🇱🇾</span>
                </div>
                <div>
                    <h1 class="text-lg font-bold gradient-text">نقطة قريبة</h1>
                    <p class="text-xs text-white/70">ليبيا 2025</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 space-x-reverse">
                <button id="adminBtn" onclick="showAdminLogin()" class="nav-button danger-button px-4 py-2 rounded-xl text-sm font-medium">
                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    إدارة
                </button>
                <button id="menuToggle" class="nav-button p-3 rounded-xl">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- قائمة الهاتف العصرية -->
        <div id="mobileMenu" class="hidden navbar-glass border-t border-white/20">
            <div class="px-6 py-4 space-y-3">
                <button onclick="scrollToSection('map')" class="w-full text-right py-3 px-4 rounded-xl nav-button text-white hover:bg-white/10 transition-all duration-300">
                    <span class="flex items-center justify-end space-x-2 space-x-reverse">
                        <span>الخريطة</span>
                        <span class="text-lg">🗺️</span>
                    </span>
                </button>
                <button onclick="scrollToSection('agents')" class="w-full text-right py-3 px-4 rounded-xl nav-button text-white hover:bg-white/10 transition-all duration-300">
                    <span class="flex items-center justify-end space-x-2 space-x-reverse">
                        <span>الوكلاء</span>
                        <span class="text-lg">🏢</span>
                    </span>
                </button>
                <button onclick="scrollToSection('contact')" class="w-full text-right py-3 px-4 rounded-xl nav-button text-white hover:bg-white/10 transition-all duration-300">
                    <span class="flex items-center justify-end space-x-2 space-x-reverse">
                        <span>اتصل بنا</span>
                        <span class="text-lg">📞</span>
                    </span>
                </button>
                <button onclick="findNearestAgent()" class="w-full text-right py-3 px-4 rounded-xl modern-button success-button">
                    <span class="flex items-center justify-center space-x-2 space-x-reverse">
                        <span>أقرب وكيل</span>
                        <span class="text-lg">📍</span>
                    </span>
                </button>
                <div class="border-t border-white/20 my-3"></div>
                <button onclick="showAdminLogin()" class="w-full text-right py-3 px-4 rounded-xl modern-button danger-button">
                    <span class="flex items-center justify-center space-x-2 space-x-reverse">
                        <span>لوحة الإدارة</span>
                        <span class="text-lg">🛡️</span>
                    </span>
                </button>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4 sm:px-6 py-8 sm:py-12">
        <!-- Header العصري للشاشات الكبيرة -->
        <div class="hidden lg:block text-center mb-16">
            <div class="relative">
                <!-- العلم الليبي المتحرك -->
                <div class="text-8xl mb-8 animate-float">
                    <div class="inline-block relative">
                        <span class="text-8xl">🇱🇾</span>
                        <div class="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full animate-ping"></div>
                    </div>
                </div>

                <!-- العنوان الرئيسي -->
                <h1 class="text-6xl md:text-8xl font-black gradient-text mb-6 tracking-tight">
                    نقطة قريبة
                </h1>

                <!-- الوصف -->
                <p class="text-2xl md:text-3xl text-gray-700 mb-8 font-light">
                    أقرب وكيل لك في ليبيا
                    <span class="block text-lg text-gray-600 mt-2">Libya 2025 • الجيل الجديد</span>
                </p>

                <!-- مؤشر الحالة العصري -->
                <div class="glass-card inline-flex items-center px-8 py-4 mb-8">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="relative">
                            <div class="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                            <div class="absolute inset-0 w-4 h-4 bg-green-400 rounded-full animate-ping"></div>
                        </div>
                        <span class="text-gray-800 font-semibold">النظام متصل ويعمل بكفاءة عالية</span>
                        <div class="text-2xl">⚡</div>
                    </div>
                </div>

                <!-- أزرار العمل السريع -->
                <div class="flex justify-center space-x-4 space-x-reverse">
                    <button onclick="scrollToSection('map')" class="modern-button px-8 py-4 text-lg">
                        🗺️ استكشف الخريطة
                    </button>
                    <button onclick="findNearestAgent()" class="modern-button success-button px-8 py-4 text-lg">
                        📍 أقرب وكيل
                    </button>
                </div>
            </div>
        </div>

        <!-- Header للهواتف -->
        <div class="lg:hidden text-center mb-8">
            <div class="glass-card p-6 mx-2">
                <div class="text-5xl mb-4 animate-float">🇱🇾</div>
                <h1 class="text-3xl font-bold gradient-text mb-2">نقطة قريبة</h1>
                <p class="text-white/80 mb-4">أقرب وكيل لك في ليبيا</p>

                <div class="flex items-center justify-center space-x-2 space-x-reverse text-sm">
                    <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                    <span class="text-white/90">متصل</span>
                </div>
            </div>
        </div>

        <!-- الخريطة العصرية -->
        <div id="map" class="glass-card p-6 mb-12 relative overflow-hidden">
            <!-- خلفية متحركة -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 animate-pulse"></div>

            <div class="relative z-10">
                <div class="flex items-center justify-between mb-6">
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center animate-glow">
                            <span class="text-2xl">🗺️</span>
                        </div>
                        <div>
                            <h2 class="text-2xl sm:text-3xl font-bold text-gray-900">خريطة الوكلاء التفاعلية</h2>
                            <p class="text-gray-600 text-sm">اكتشف جميع الوكلاء في ليبيا</p>
                        </div>
                    </div>

                    <div class="flex space-x-2 space-x-reverse">
                        <button onclick="refreshMap()" class="nav-button p-3 rounded-xl" title="تحديث الخريطة">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                        </button>
                        <button onclick="toggleFullscreenMap()" class="lg:hidden nav-button p-3 rounded-xl" title="ملء الشاشة">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- إحصائيات سريعة -->
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-gray-900" id="mapAgentsCount">5</div>
                        <div class="text-gray-600 text-sm">وكيل</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-gray-900" id="mapCitiesCount">5</div>
                        <div class="text-gray-600 text-sm">مدينة</div>
                    </div>
                    <div class="glass-card p-4 text-center">
                        <div class="text-2xl font-bold text-gray-900">4.7</div>
                        <div class="text-gray-600 text-sm">⭐ التقييم</div>
                    </div>
                </div>

                <div id="mapContainer" class="map-container relative rounded-2xl overflow-hidden border border-white/20">
                    <!-- زر إغلاق الخريطة المكبرة -->
                    <button id="closeFullscreenMap" onclick="closeFullscreenMap()" class="hidden absolute top-6 left-6 z-10 nav-button p-3 rounded-xl">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>

                    <!-- مؤشر التحميل -->
                    <div id="mapLoader" class="absolute inset-0 bg-black/50 flex items-center justify-center z-20">
                        <div class="glass-card p-6 text-center">
                            <div class="animate-spin w-8 h-8 border-4 border-white/30 border-t-white rounded-full mx-auto mb-3"></div>
                            <p class="text-white">جاري تحميل الخريطة...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الوكلاء العصرية -->
        <div id="agents" class="mb-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl sm:text-4xl font-bold gradient-text mb-4">🏢 شبكة الوكلاء</h2>
                <p class="text-gray-700 text-lg">اكتشف أفضل الوكلاء في جميع أنحاء ليبيا</p>
            </div>

            <!-- بطاقة المكتب الرئيسي المميزة -->
            <div class="hidden sm:block mb-12">
                <div class="featured-office-card p-8 mx-auto max-w-4xl">
                    <div class="flex items-start justify-between mb-6">
                        <div class="flex items-center space-x-4 space-x-reverse">
                            <div class="main-office-icon">
                                🏛️
                            </div>
                            <div>
                                <div class="flex items-center space-x-3 space-x-reverse mb-2">
                                    <h2 class="text-3xl font-black text-gray-900">المكتب الرئيسي</h2>
                                    <span class="main-office-badge">الرئيسي</span>
                                </div>
                                <p class="text-lg text-gray-700 font-medium">المقر الرئيسي لشركة نقطة قريبة في ليبيا</p>
                            </div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- منطقة الصورة -->
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-bold text-gray-900">📸 صورة المكتب</h3>
                                <!-- شارة المدير -->
                                <div id="adminBadge" class="hidden">
                                    <span class="bg-red-100 text-red-800 text-xs font-bold px-3 py-1 rounded-full border border-red-300">
                                        🛡️ وضع المدير
                                    </span>
                                </div>
                            </div>

                            <div class="office-image-container" onclick="handleImageClick()">
                                <img id="officeImage" class="office-image hidden" alt="صورة المكتب الرئيسي">
                                <div id="imagePlaceholder" class="text-center">
                                    <div class="text-6xl text-gray-400 mb-4">🏢</div>
                                    <div id="imageInstructions">
                                        <p class="text-gray-600 font-medium">صورة المكتب الرئيسي</p>
                                        <p class="text-gray-500 text-sm mt-2">يمكن للمدير فقط تعديل الصورة</p>
                                    </div>
                                </div>
                                <div class="image-upload-overlay" id="imageUploadOverlay" style="display: none;">
                                    <div class="text-center text-white">
                                        <div class="text-3xl mb-2">📷</div>
                                        <p class="font-medium">تغيير الصورة</p>
                                    </div>
                                </div>
                            </div>

                            <!-- أزرار التحكم للمدير -->
                            <div id="adminImageControls" class="hidden space-y-2">
                                <div class="flex space-x-2 space-x-reverse">
                                    <button onclick="triggerImageUpload()" class="flex-1 modern-button py-2 text-sm">
                                        📷 رفع صورة جديدة
                                    </button>
                                    <button onclick="removeOfficeImage()" class="modern-button danger-button px-4 py-2 text-sm">
                                        🗑️ حذف
                                    </button>
                                </div>
                                <p class="text-xs text-gray-500 text-center">JPG, PNG, GIF (حد أقصى 5MB)</p>
                            </div>

                            <input type="file" id="imageUpload" accept="image/*" class="hidden" onchange="handleImageUpload(event)">
                        </div>

                        <!-- معلومات المكتب -->
                        <div class="space-y-6">
                            <div class="grid grid-cols-1 gap-4">
                                <!-- العنوان -->
                                <div class="agent-card p-4">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-xl">
                                            📍
                                        </div>
                                        <div>
                                            <h4 class="font-bold text-gray-900">العنوان</h4>
                                            <p class="text-gray-700">شارع عمر المختار، وسط البلد، طرابلس</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- الهاتف -->
                                <div class="agent-card p-4">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center text-white text-xl">
                                            📞
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="font-bold text-gray-900">الهاتف الرئيسي</h4>
                                            <a href="tel:+218212345678" class="text-blue-600 hover:text-blue-800 font-medium">+218 21 234 5678</a>
                                        </div>
                                        <button onclick="window.open('tel:+218212345678')" class="modern-button success-button px-4 py-2">
                                            📞 اتصل
                                        </button>
                                    </div>
                                </div>

                                <!-- ساعات العمل -->
                                <div class="agent-card p-4">
                                    <div class="flex items-center space-x-3 space-x-reverse">
                                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-white text-xl">
                                            🕒
                                        </div>
                                        <div>
                                            <h4 class="font-bold text-gray-900">ساعات العمل</h4>
                                            <p class="text-gray-700">السبت - الخميس: 8:00 ص - 6:00 م</p>
                                            <span class="inline-block mt-1 px-3 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                                                ✅ مفتوح الآن
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <!-- التقييم والخدمات -->
                                <div class="agent-card p-4">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-3 space-x-reverse">
                                            <div class="w-12 h-12 rounded-full bg-gradient-to-r from-yellow-500 to-orange-600 flex items-center justify-center text-white text-xl">
                                                ⭐
                                            </div>
                                            <div>
                                                <h4 class="font-bold text-gray-900">التقييم</h4>
                                                <div class="flex items-center space-x-2 space-x-reverse">
                                                    <span class="text-yellow-500 text-lg">⭐⭐⭐⭐⭐</span>
                                                    <span class="font-bold text-gray-900">4.9</span>
                                                    <span class="text-gray-600 text-sm">(324 تقييم)</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="text-center">
                                            <button onclick="showOfficeDetails()" class="modern-button px-6 py-3">
                                                🏢 تفاصيل أكثر
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض شبكي عصري للشاشات الكبيرة -->
            <div class="hidden sm:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- وكيل طرابلس -->
                <div class="agent-card p-6 group transition-all duration-500 relative overflow-hidden">
                    <!-- تأثير الضوء -->
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <!-- أيقونة المدينة -->
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl animate-glow shadow-lg">
                                🏢
                            </div>
                            <div class="text-bg-dark px-3 py-1 rounded-full">
                                <span class="text-white-enhanced text-sm font-bold">طرابلس</span>
                            </div>
                        </div>

                        <!-- معلومات الوكيل -->
                        <h3 class="text-xl agent-title mb-3">وكيل طرابلس المركزي</h3>
                        <p class="text-sm agent-location mb-4">شارع عمر المختار، وسط البلد</p>

                        <!-- معلومات الاتصال -->
                        <div class="space-y-2 mb-4">
                            <div class="flex items-center space-x-2 space-x-reverse text-bg-dark rounded-lg px-3 py-2">
                                <span class="text-green-400 text-lg">📞</span>
                                <span class="text-white-enhanced text-sm font-medium">+218 21 234 5678</span>
                            </div>
                            <div class="flex items-center justify-between text-bg-light rounded-lg px-3 py-2">
                                <div class="flex items-center space-x-1 space-x-reverse">
                                    <span class="text-yellow-400 text-lg">⭐⭐⭐⭐⭐</span>
                                    <span class="text-white-enhanced text-sm font-bold">4.8</span>
                                </div>
                                <span class="text-white-soft text-xs">156 تقييم</span>
                            </div>
                        </div>

                        <!-- أزرار العمل -->
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="showAgentDetails('طرابلس')" class="flex-1 modern-button py-3 text-sm font-bold">
                                📍 عرض التفاصيل
                            </button>
                            <button onclick="window.open('tel:+218212345678')" class="modern-button success-button px-4 py-3 font-bold">
                                📞
                            </button>
                        </div>
                    </div>
                </div>

                <!-- وكيل بنغازي -->
                <div class="agent-card p-6 group transition-all duration-500 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-green-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center text-2xl animate-glow shadow-lg">
                                🏢
                            </div>
                            <div class="text-bg-dark px-3 py-1 rounded-full">
                                <span class="text-white-enhanced text-sm font-bold">بنغازي</span>
                            </div>
                        </div>

                        <h3 class="text-xl agent-title mb-3">وكيل بنغازي الشرقي</h3>
                        <p class="text-sm agent-location mb-4">شارع جمال عبد الناصر، حي الصابري</p>

                        <div class="space-y-2 mb-4">
                            <div class="flex items-center space-x-2 space-x-reverse text-bg-dark rounded-lg px-3 py-2">
                                <span class="text-green-400 text-lg">📞</span>
                                <span class="text-white-enhanced text-sm font-medium">+218 61 234 5678</span>
                            </div>
                            <div class="flex items-center justify-between text-bg-light rounded-lg px-3 py-2">
                                <div class="flex items-center space-x-1 space-x-reverse">
                                    <span class="text-yellow-400 text-lg">⭐⭐⭐⭐⭐</span>
                                    <span class="text-white-enhanced text-sm font-bold">4.7</span>
                                </div>
                                <span class="text-white-soft text-xs">134 تقييم</span>
                            </div>
                        </div>

                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="showAgentDetails('بنغازي')" class="flex-1 modern-button py-3 text-sm font-bold">
                                📍 عرض التفاصيل
                            </button>
                            <button onclick="window.open('tel:+218612345678')" class="modern-button success-button px-4 py-3 font-bold">
                                📞
                            </button>
                        </div>
                    </div>
                </div>

                <!-- وكيل مصراتة -->
                <div class="agent-card p-6 group transition-all duration-500 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center text-2xl animate-glow shadow-lg">
                                🏢
                            </div>
                            <div class="text-bg-dark px-3 py-1 rounded-full">
                                <span class="text-white-enhanced text-sm font-bold">مصراتة</span>
                            </div>
                        </div>

                        <h3 class="text-xl agent-title mb-3">وكيل مصراتة التجاري</h3>
                        <p class="text-sm agent-location mb-4">شارع طرابلس، المنطقة التجارية</p>

                        <div class="space-y-2 mb-4">
                            <div class="flex items-center space-x-2 space-x-reverse text-bg-dark rounded-lg px-3 py-2">
                                <span class="text-green-400 text-lg">📞</span>
                                <span class="text-white-enhanced text-sm font-medium">+218 51 334 5678</span>
                            </div>
                            <div class="flex items-center justify-between text-bg-light rounded-lg px-3 py-2">
                                <div class="flex items-center space-x-1 space-x-reverse">
                                    <span class="text-yellow-400 text-lg">⭐⭐⭐⭐⭐</span>
                                    <span class="text-white-enhanced text-sm font-bold">4.6</span>
                                </div>
                                <span class="text-white-soft text-xs">98 تقييم</span>
                            </div>
                        </div>

                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="showAgentDetails('مصراتة')" class="flex-1 modern-button py-3 text-sm font-bold">
                                📍 عرض التفاصيل
                            </button>
                            <button onclick="window.open('tel:+218513345678')" class="modern-button success-button px-4 py-3 font-bold">
                                📞
                            </button>
                        </div>
                    </div>
                </div>

                <!-- وكيل سبها -->
                <div class="glass-card p-6 group hover:scale-105 transition-all duration-500 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-orange-500 to-red-600 flex items-center justify-center text-2xl animate-glow">
                                🏢
                            </div>
                            <div class="glass-card px-3 py-1 rounded-full">
                                <span class="text-white text-sm font-medium">سبها</span>
                            </div>
                        </div>

                        <h3 class="text-xl agent-title mb-3">وكيل سبها الجنوبي</h3>
                        <p class="text-sm agent-location mb-4">شارع الجمهورية، وسط المدينة</p>

                        <div class="space-y-2 mb-4">
                            <div class="flex items-center space-x-2 space-x-reverse text-white/80">
                                <span class="text-green-400">📞</span>
                                <span class="text-sm">+218 71 334 5678</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-1 space-x-reverse">
                                    <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                    <span class="text-white/70 text-sm">4.5</span>
                                </div>
                                <span class="text-white/60 text-xs">67 تقييم</span>
                            </div>
                        </div>

                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="showAgentDetails('سبها')" class="flex-1 modern-button py-3 text-sm">
                                📍 عرض
                            </button>
                            <button onclick="window.open('tel:+218713345678')" class="modern-button success-button px-4 py-3">
                                📞
                            </button>
                        </div>
                    </div>
                </div>

                <!-- وكيل الزاوية -->
                <div class="glass-card p-6 group hover:scale-105 transition-all duration-500 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-teal-500/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <div class="relative z-10">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-16 h-16 rounded-full bg-gradient-to-r from-teal-500 to-cyan-600 flex items-center justify-center text-2xl animate-glow">
                                🏢
                            </div>
                            <div class="glass-card px-3 py-1 rounded-full">
                                <span class="text-white text-sm font-medium">الزاوية</span>
                            </div>
                        </div>

                        <h3 class="text-xl agent-title mb-3">وكيل الزاوية الغربي</h3>
                        <p class="text-sm agent-location mb-4">شارع الشهداء، حي النصر</p>

                        <div class="space-y-2 mb-4">
                            <div class="flex items-center space-x-2 space-x-reverse text-white/80">
                                <span class="text-green-400">📞</span>
                                <span class="text-sm">+218 23 334 5678</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-1 space-x-reverse">
                                    <span class="text-yellow-400">⭐⭐⭐⭐⭐</span>
                                    <span class="text-white/70 text-sm">4.4</span>
                                </div>
                                <span class="text-white/60 text-xs">92 تقييم</span>
                            </div>
                        </div>

                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="showAgentDetails('الزاوية')" class="flex-1 modern-button py-3 text-sm">
                                📍 عرض
                            </button>
                            <button onclick="window.open('tel:+218233345678')" class="modern-button success-button px-4 py-3">
                                📞
                            </button>
                        </div>
                    </div>
                </div>

                <!-- بطاقة البحث الذكي -->
                <div class="glass-card p-6 relative overflow-hidden bg-gradient-to-br from-indigo-500/30 to-purple-600/30">
                    <div class="absolute inset-0 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 animate-pulse"></div>

                    <div class="relative z-10 text-center">
                        <div class="w-20 h-20 rounded-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center text-3xl mx-auto mb-4 animate-glow">
                            🎯
                        </div>

                        <h3 class="text-2xl agent-title mb-4">البحث الذكي</h3>
                        <p class="agent-location mb-6">استخدم موقعك الحالي للعثور على أقرب وكيل بتقنية الذكاء الاصطناعي</p>

                        <button onclick="findNearestAgent()" class="w-full modern-button py-4 text-lg font-semibold">
                            <span class="flex items-center justify-center space-x-2 space-x-reverse">
                                <span>🔍 ابحث الآن</span>
                                <span class="animate-pulse">📍</span>
                            </span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- بطاقة المكتب الرئيسي للهواتف -->
            <div class="sm:hidden mb-8 px-2">
                <div class="featured-office-card p-6">
                    <div class="text-center mb-6">
                        <div class="main-office-icon mx-auto mb-4">
                            🏛️
                        </div>
                        <div class="flex items-center justify-center space-x-2 space-x-reverse mb-2">
                            <h2 class="text-2xl font-black text-gray-900">المكتب الرئيسي</h2>
                            <span class="main-office-badge">الرئيسي</span>
                        </div>
                        <p class="text-gray-700">المقر الرئيسي لشركة نقطة قريبة</p>
                    </div>

                    <!-- منطقة الصورة للهواتف -->
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium text-gray-700">📸 صورة المكتب</span>
                            <div id="adminBadgeMobile" class="hidden">
                                <span class="bg-red-100 text-red-800 text-xs font-bold px-2 py-1 rounded-full">
                                    🛡️ مدير
                                </span>
                            </div>
                        </div>

                        <div class="office-image-container h-48" onclick="handleImageClickMobile()">
                            <img id="officeImageMobile" class="office-image hidden" alt="صورة المكتب الرئيسي">
                            <div id="imagePlaceholderMobile" class="text-center">
                                <div class="text-4xl text-gray-400 mb-2">🏢</div>
                                <div id="imageInstructionsMobile">
                                    <p class="text-gray-600 text-sm">صورة المكتب الرئيسي</p>
                                    <p class="text-gray-500 text-xs mt-1">للمدير فقط</p>
                                </div>
                            </div>
                            <div class="image-upload-overlay" id="imageUploadOverlayMobile" style="display: none;">
                                <div class="text-center text-white">
                                    <div class="text-2xl mb-1">📷</div>
                                    <p class="text-sm">تغيير</p>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم للمدير - الهواتف -->
                        <div id="adminImageControlsMobile" class="hidden mt-3">
                            <div class="grid grid-cols-2 gap-2">
                                <button onclick="triggerImageUpload()" class="modern-button py-2 text-xs">
                                    📷 رفع صورة
                                </button>
                                <button onclick="removeOfficeImage()" class="modern-button danger-button py-2 text-xs">
                                    🗑️ حذف
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات سريعة -->
                    <div class="space-y-3">
                        <div class="flex items-center justify-between agent-card p-3">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-xl">📍</span>
                                <span class="text-gray-900 font-medium text-sm">طرابلس، وسط البلد</span>
                            </div>
                        </div>

                        <div class="flex items-center justify-between agent-card p-3">
                            <div class="flex items-center space-x-2 space-x-reverse">
                                <span class="text-xl">⭐</span>
                                <span class="text-gray-900 font-medium text-sm">4.9 (324 تقييم)</span>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-3">
                            <button onclick="window.open('tel:+218212345678')" class="modern-button success-button py-3 text-sm">
                                📞 اتصل
                            </button>
                            <button onclick="showOfficeDetails()" class="modern-button py-3 text-sm">
                                🏢 التفاصيل
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- عرض الهواتف المحمولة - قائمة عمودية -->
            <div class="sm:hidden space-y-4 px-2">
                <!-- وكيل طرابلس -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="text-2xl">🏢</div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">وكيل طرابلس المركزي</h3>
                            <p class="text-sm text-gray-600 mb-2">شارع عمر المختار، وسط البلد</p>
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-yellow-500 text-sm">⭐⭐⭐⭐⭐</span>
                                <span class="text-xs text-gray-500">4.8 (156)</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="tel:+218212345678" class="flex-1 touch-button bg-green-600 text-white py-2 px-3 rounded-lg text-center text-sm hover:bg-green-700 transition-colors">
                                    📞 اتصل
                                </a>
                                <button onclick="showAgentDetails('طرابلس')" class="flex-1 touch-button bg-blue-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                    📍 موقع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- وكيل بنغازي -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="text-2xl">🏢</div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">وكيل بنغازي الشرقي</h3>
                            <p class="text-sm text-gray-600 mb-2">شارع جمال عبد الناصر، حي الصابري</p>
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-yellow-500 text-sm">⭐⭐⭐⭐⭐</span>
                                <span class="text-xs text-gray-500">4.7 (134)</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="tel:+218612345678" class="flex-1 touch-button bg-green-600 text-white py-2 px-3 rounded-lg text-center text-sm hover:bg-green-700 transition-colors">
                                    📞 اتصل
                                </a>
                                <button onclick="showAgentDetails('بنغازي')" class="flex-1 touch-button bg-blue-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                    📍 موقع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- وكيل مصراتة -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="text-2xl">🏢</div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">وكيل مصراتة التجاري</h3>
                            <p class="text-sm text-gray-600 mb-2">شارع طرابلس، المنطقة التجارية</p>
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-yellow-500 text-sm">⭐⭐⭐⭐⭐</span>
                                <span class="text-xs text-gray-500">4.6 (98)</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="tel:+218513345678" class="flex-1 touch-button bg-green-600 text-white py-2 px-3 rounded-lg text-center text-sm hover:bg-green-700 transition-colors">
                                    📞 اتصل
                                </a>
                                <button onclick="showAgentDetails('مصراتة')" class="flex-1 touch-button bg-blue-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                    📍 موقع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- وكيل سبها -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="text-2xl">🏢</div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">وكيل سبها الجنوبي</h3>
                            <p class="text-sm text-gray-600 mb-2">شارع الجمهورية، وسط المدينة</p>
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-yellow-500 text-sm">⭐⭐⭐⭐⭐</span>
                                <span class="text-xs text-gray-500">4.5 (67)</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="tel:+218713345678" class="flex-1 touch-button bg-green-600 text-white py-2 px-3 rounded-lg text-center text-sm hover:bg-green-700 transition-colors">
                                    📞 اتصل
                                </a>
                                <button onclick="showAgentDetails('سبها')" class="flex-1 touch-button bg-blue-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                    📍 موقع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- وكيل الزاوية -->
                <div class="bg-white p-4 rounded-lg shadow-md">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="text-2xl">🏢</div>
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900 mb-1">وكيل الزاوية الغربي</h3>
                            <p class="text-sm text-gray-600 mb-2">شارع الشهداء، حي النصر</p>
                            <div class="flex items-center justify-between mb-3">
                                <span class="text-yellow-500 text-sm">⭐⭐⭐⭐⭐</span>
                                <span class="text-xs text-gray-500">4.4 (92)</span>
                            </div>
                            <div class="flex space-x-2 space-x-reverse">
                                <a href="tel:+218233345678" class="flex-1 touch-button bg-green-600 text-white py-2 px-3 rounded-lg text-center text-sm hover:bg-green-700 transition-colors">
                                    📞 اتصل
                                </a>
                                <button onclick="showAgentDetails('الزاوية')" class="flex-1 touch-button bg-blue-600 text-white py-2 px-3 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                                    📍 موقع
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- زر البحث عن أقرب وكيل -->
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-lg shadow-md">
                    <div class="text-center">
                        <div class="text-3xl mb-2">🎯</div>
                        <h3 class="font-semibold mb-2">ابحث عن أقرب وكيل</h3>
                        <p class="text-sm mb-4 opacity-90">استخدم موقعك الحالي للعثور على أقرب وكيل</p>
                        <button onclick="findNearestAgent()" class="w-full touch-button bg-white text-blue-600 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                            📍 حدد موقعي
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات الاتصال العصرية -->
        <div id="contact" class="glass-card p-8 relative overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-purple-500/10 animate-pulse"></div>

            <div class="relative z-10">
                <div class="text-center mb-12">
                    <div class="w-20 h-20 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-3xl mx-auto mb-6 animate-glow">
                        📞
                    </div>
                    <h2 class="text-3xl sm:text-4xl font-bold gradient-text mb-4">تواصل معنا</h2>
                    <p class="text-gray-700 text-lg">نحن هنا لخدمتك على مدار الساعة</p>
                </div>

                <!-- عرض الشاشات الكبيرة العصري -->
                <div class="hidden sm:grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- الهاتف الرئيسي -->
                    <div class="glass-card p-6 text-center group hover:scale-105 transition-all duration-500">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center text-2xl mx-auto mb-4 animate-glow">
                            📱
                        </div>
                        <h3 class="text-lg font-bold text-gray-900 mb-2">الهاتف الرئيسي</h3>
                        <a href="tel:+218212345678" class="text-gray-700 hover:text-gray-900 transition-colors">+218 21 234 5678</a>
                    </div>

                    <!-- واتساب -->
                    <div class="glass-card p-6 text-center group hover:scale-105 transition-all duration-500">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-green-400 to-green-600 flex items-center justify-center text-2xl mx-auto mb-4 animate-glow">
                            💬
                        </div>
                        <h3 class="text-lg font-bold text-white mb-2">واتساب</h3>
                        <a href="https://wa.me/************" class="text-white/80 hover:text-white transition-colors">+218 91 123 4567</a>
                    </div>

                    <!-- البريد الإلكتروني -->
                    <div class="glass-card p-6 text-center group hover:scale-105 transition-all duration-500">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl mx-auto mb-4 animate-glow">
                            📧
                        </div>
                        <h3 class="text-lg font-bold text-white mb-2">البريد الإلكتروني</h3>
                        <a href="mailto:<EMAIL>" class="text-white/80 hover:text-white transition-colors text-sm"><EMAIL></a>
                    </div>

                    <!-- العنوان -->
                    <div class="glass-card p-6 text-center group hover:scale-105 transition-all duration-500">
                        <div class="w-16 h-16 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center text-2xl mx-auto mb-4 animate-glow">
                            📍
                        </div>
                        <h3 class="text-lg font-bold text-white mb-2">العنوان</h3>
                        <p class="text-white/80 text-sm">شارع عمر المختار، وسط البلد، طرابلس</p>
                    </div>
                </div>

                <!-- عرض الهواتف المحمولة العصري -->
                <div class="sm:hidden space-y-4">
                    <a href="tel:+218212345678" class="glass-card p-4 flex items-center hover:scale-105 transition-all duration-300">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center text-xl ml-4 animate-glow">
                            📱
                        </div>
                        <div>
                            <h3 class="font-bold text-white">الهاتف الرئيسي</h3>
                            <p class="text-white/80">+218 21 234 5678</p>
                        </div>
                    </a>

                    <a href="https://wa.me/************" class="glass-card p-4 flex items-center hover:scale-105 transition-all duration-300">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-green-400 to-green-600 flex items-center justify-center text-xl ml-4 animate-glow">
                            💬
                        </div>
                        <div>
                            <h3 class="font-bold text-white">واتساب</h3>
                            <p class="text-white/80">+218 91 123 4567</p>
                        </div>
                    </a>

                    <a href="mailto:<EMAIL>" class="glass-card p-4 flex items-center hover:scale-105 transition-all duration-300">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-xl ml-4 animate-glow">
                            📧
                        </div>
                        <div>
                            <h3 class="font-bold text-white">البريد الإلكتروني</h3>
                            <p class="text-white/80 text-sm"><EMAIL></p>
                        </div>
                    </a>

                    <div class="glass-card p-4 flex items-center">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-600 flex items-center justify-center text-xl ml-4 animate-glow">
                            📍
                        </div>
                        <div>
                            <h3 class="font-bold text-white">العنوان</h3>
                            <p class="text-white/80 text-sm">شارع عمر المختار، وسط البلد، طرابلس</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-gray-600">
            <p>© 2024 نقطة قريبة - جميع الحقوق محفوظة</p>
            <p class="text-sm mt-2">مصمم خصيصاً لليبيا بـ ❤️</p>
        </div>
    </div>

    <!-- نافذة تسجيل دخول المدير -->
    <div id="adminLoginModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-auto">
            <!-- رأس النافذة -->
            <div class="flex items-center justify-between p-6 border-b bg-red-50">
                <h2 class="text-xl font-bold text-red-900">🛡️ دخول المدير</h2>
                <button onclick="closeAdminLogin()" class="touch-button p-2 rounded-lg hover:bg-red-100 transition-colors">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- محتوى النافذة -->
            <div class="p-6">
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-yellow-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <span class="text-yellow-800 text-sm font-medium">منطقة محظورة - للمديرين فقط</span>
                    </div>
                </div>

                <form id="adminLoginForm" onsubmit="handleAdminLogin(event)">
                    <!-- حقل اسم المستخدم -->
                    <div class="mb-4">
                        <label for="adminUsername" class="block text-sm font-medium text-gray-700 mb-2">
                            👤 اسم المستخدم
                        </label>
                        <input
                            type="text"
                            id="adminUsername"
                            name="username"
                            placeholder="أدخل اسم المستخدم"
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors text-right"
                            required
                        >
                    </div>

                    <!-- حقل كلمة المرور -->
                    <div class="mb-6">
                        <label for="adminPassword" class="block text-sm font-medium text-gray-700 mb-2">
                            🔒 كلمة المرور
                        </label>
                        <div class="relative">
                            <input
                                type="password"
                                id="adminPassword"
                                name="password"
                                placeholder="أدخل كلمة مرور المدير"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors text-right"
                                required
                                minlength="6"
                            >
                            <button
                                type="button"
                                onclick="toggleAdminPassword()"
                                class="absolute left-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                            >
                                <svg id="adminEyeIcon" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>

                    <!-- زر تسجيل الدخول -->
                    <button
                        type="submit"
                        class="w-full touch-button bg-red-600 text-white py-3 rounded-lg font-medium hover:bg-red-700 transition-colors"
                    >
                        🛡️ دخول لوحة الإدارة
                    </button>
                </form>

                <!-- رسائل الحالة -->
                <div id="adminLoginMessage" class="hidden mt-4 p-3 rounded-lg text-sm text-center"></div>
            </div>

            <!-- معلومات المدير -->
            <div class="bg-red-50 px-6 py-4 rounded-b-lg border-t">
                <div class="text-center">
                    <p class="text-xs text-red-700 mb-2">🔐 بيانات المدير التجريبية:</p>
                    <div class="text-xs text-red-600">
                        <p>المستخدم: <code class="bg-red-100 px-1 rounded">admin</code></p>
                        <p>كلمة المرور: <code class="bg-red-100 px-1 rounded">admin123</code></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- لوحة تحكم المدير -->
    <div id="adminDashboard" class="hidden fixed inset-0 bg-gray-100 z-50 overflow-y-auto">
        <!-- شريط علوي -->
        <div class="bg-white shadow-sm border-b">
            <div class="flex items-center justify-between px-6 py-4">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-900">لوحة تحكم المدير</h1>
                        <p class="text-sm text-gray-600">نقطة قريبة - ليبيا</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3 space-x-reverse">
                    <span class="text-sm text-gray-600">مرحباً، <span id="adminName" class="font-semibold">المدير</span></span>
                    <button onclick="logoutAdmin()" class="touch-button px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                        خروج
                    </button>
                </div>
            </div>
        </div>

        <!-- المحتوى الرئيسي -->
        <div class="flex">
            <!-- القائمة الجانبية -->
            <div class="w-64 bg-white shadow-sm h-screen sticky top-0">
                <nav class="p-4">
                    <ul class="space-y-2">
                        <li>
                            <button onclick="showDashboardSection('overview')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn active">
                                📊 نظرة عامة
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('agents')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                🏢 إدارة الوكلاء
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('users')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                👥 إدارة المستخدمين
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('analytics')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                📈 التحليلات
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('settings')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                ⚙️ الإعدادات
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('system')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                🛠️ إدارة النظام
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('backup')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                💾 النسخ الاحتياطي
                            </button>
                        </li>
                        <li>
                            <button onclick="showDashboardSection('logs')" class="w-full text-right px-4 py-3 rounded-lg hover:bg-gray-100 transition-colors dashboard-nav-btn">
                                📋 سجل الأنشطة
                            </button>
                        </li>
                    </ul>
                </nav>
            </div>

            <!-- المحتوى -->
            <div class="flex-1 p-6">
                <!-- نظرة عامة -->
                <div id="overview-section" class="dashboard-section">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">📊 نظرة عامة</h2>

                    <!-- بطاقات الإحصائيات -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">إجمالي الوكلاء</p>
                                    <p class="text-2xl font-bold text-gray-900" id="totalAgents">5</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">المستخدمين النشطين</p>
                                    <p class="text-2xl font-bold text-gray-900" id="activeUsers">127</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">البحثات اليوم</p>
                                    <p class="text-2xl font-bold text-gray-900" id="todaySearches">89</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                </div>
                                <div class="mr-4">
                                    <p class="text-sm font-medium text-gray-600">التقييم العام</p>
                                    <p class="text-2xl font-bold text-gray-900" id="overallRating">4.7</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- الأنشطة الأخيرة -->
                    <div class="bg-white rounded-lg shadow-sm border p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">📋 الأنشطة الأخيرة</h3>
                        <div class="space-y-4" id="recentActivities">
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3">
                                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">مستخدم جديد انضم للنظام</p>
                                    <p class="text-xs text-gray-500">منذ 5 دقائق</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center ml-3">
                                    <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">تم تحديث بيانات وكيل طرابلس</p>
                                    <p class="text-xs text-gray-500">منذ 15 دقيقة</p>
                                </div>
                            </div>
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center ml-3">
                                    <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900">25 عملية بحث جديدة</p>
                                    <p class="text-xs text-gray-500">منذ ساعة</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إدارة الوكلاء -->
                <div id="agents-section" class="dashboard-section hidden">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">🏢 إدارة الوكلاء</h2>
                        <button onclick="showAddAgentModal()" class="touch-button px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            ➕ إضافة وكيل جديد
                        </button>
                    </div>

                    <!-- جدول الوكلاء -->
                    <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الوكيل</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المدينة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الهاتف</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التقييم</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="agentsTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- إدارة المستخدمين -->
                <div id="users-section" class="dashboard-section hidden">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">👥 إدارة المستخدمين</h2>
                        <button onclick="showAddUserModal()" class="touch-button px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            ➕ إضافة مستخدم
                        </button>
                    </div>

                    <!-- جدول المستخدمين -->
                    <div class="bg-white rounded-lg shadow-sm border overflow-hidden">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">البريد/الهاتف</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ الانضمام</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody" class="bg-white divide-y divide-gray-200">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- التحليلات -->
                <div id="analytics-section" class="dashboard-section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">📈 التحليلات والإحصائيات</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- إحصائيات البحث -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🔍 إحصائيات البحث</h3>
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">البحثات اليوم</span>
                                    <span class="font-semibold text-blue-600">89</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">البحثات هذا الأسبوع</span>
                                    <span class="font-semibold text-green-600">456</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">البحثات هذا الشهر</span>
                                    <span class="font-semibold text-purple-600">1,234</span>
                                </div>
                            </div>
                        </div>

                        <!-- الوكلاء الأكثر شعبية -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🏆 الوكلاء الأكثر شعبية</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center ml-3">
                                            <span class="text-yellow-600 font-bold">1</span>
                                        </div>
                                        <span class="text-gray-900">وكيل طرابلس</span>
                                    </div>
                                    <span class="text-sm text-gray-500">45 زيارة</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center ml-3">
                                            <span class="text-gray-600 font-bold">2</span>
                                        </div>
                                        <span class="text-gray-900">وكيل بنغازي</span>
                                    </div>
                                    <span class="text-sm text-gray-500">32 زيارة</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center ml-3">
                                            <span class="text-orange-600 font-bold">3</span>
                                        </div>
                                        <span class="text-gray-900">وكيل مصراتة</span>
                                    </div>
                                    <span class="text-sm text-gray-500">28 زيارة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الإعدادات -->
                <div id="settings-section" class="dashboard-section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">⚙️ إعدادات النظام</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- إعدادات عامة -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🌐 إعدادات عامة</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                                    <input type="text" value="نقطة قريبة" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف الموقع</label>
                                    <textarea rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">أقرب وكيل لك في ليبيا</textarea>
                                </div>
                                <button class="touch-button px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>

                        <!-- إعدادات الأمان -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🔒 إعدادات الأمان</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">تغيير كلمة مرور المدير</label>
                                    <input type="password" placeholder="كلمة المرور الجديدة" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور</label>
                                    <input type="password" placeholder="تأكيد كلمة المرور" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500">
                                </div>
                                <button class="touch-button px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                    تحديث كلمة المرور
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- إدارة النظام -->
                <div id="system-section" class="dashboard-section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">🛠️ إدارة النظام</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- إعدادات النظام -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🌐 إعدادات النظام</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم الموقع</label>
                                    <input
                                        type="text"
                                        id="systemSiteName"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">وصف الموقع</label>
                                    <textarea
                                        rows="3"
                                        id="systemSiteDescription"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    ></textarea>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">الحد الأقصى للوكلاء</label>
                                    <input
                                        type="number"
                                        id="systemMaxAgents"
                                        min="1"
                                        max="1000"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    >
                                </div>
                            </div>

                            <!-- زر حفظ الإعدادات -->
                            <div class="mt-6 pt-4 border-t">
                                <button
                                    onclick="saveSystemSettings()"
                                    class="w-full modern-button success-button py-3 text-lg font-bold"
                                >
                                    💾 حفظ الإعدادات
                                </button>
                            </div>
                        </div>

                        <!-- إعدادات الأمان -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🔒 إعدادات الأمان</h3>
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">كلمة مرور المدير</label>
                                    <div class="flex space-x-2 space-x-reverse">
                                        <input
                                            type="password"
                                            id="systemAdminPassword"
                                            placeholder="كلمة المرور الجديدة"
                                            class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-red-500"
                                        >
                                        <button
                                            onclick="updateAdminPassword()"
                                            class="touch-button px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                                        >
                                            تحديث
                                        </button>
                                    </div>
                                </div>

                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="systemMaintenanceMode"
                                            class="rounded border-gray-300 text-red-600 focus:ring-red-500"
                                        >
                                        <span class="mr-2 text-sm text-gray-700">وضع الصيانة</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="systemAutoBackup"
                                            class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                        >
                                        <span class="mr-2 text-sm text-gray-700">النسخ الاحتياطي التلقائي</span>
                                    </label>

                                    <label class="flex items-center">
                                        <input
                                            type="checkbox"
                                            id="systemEmailNotifications"
                                            class="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                        >
                                        <span class="mr-2 text-sm text-gray-700">إشعارات البريد الإلكتروني</span>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات النظام -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">📊 إحصائيات النظام</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">حجم البيانات المحفوظة</span>
                                    <span id="dataSize" class="font-semibold text-blue-600">--</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">آخر نسخة احتياطية</span>
                                    <span id="lastBackup" class="font-semibold text-green-600">--</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">عدد الأنشطة المسجلة</span>
                                    <span id="activitiesCount" class="font-semibold text-purple-600">--</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">وقت تشغيل النظام</span>
                                    <span id="uptime" class="font-semibold text-orange-600">--</span>
                                </div>
                            </div>
                        </div>

                        <!-- أدوات النظام -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">🔧 أدوات النظام</h3>
                            <div class="space-y-3">
                                <button
                                    onclick="clearAllData()"
                                    class="w-full touch-button px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-center"
                                >
                                    🗑️ مسح جميع البيانات
                                </button>
                                <button
                                    onclick="resetToDefaults()"
                                    class="w-full touch-button px-4 py-3 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors text-center"
                                >
                                    🔄 إعادة تعيين للافتراضي
                                </button>
                                <button
                                    onclick="optimizeDatabase()"
                                    class="w-full touch-button px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-center"
                                >
                                    ⚡ تحسين قاعدة البيانات
                                </button>
                                <button
                                    onclick="generateReport()"
                                    class="w-full touch-button px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-center"
                                >
                                    📄 إنشاء تقرير شامل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- النسخ الاحتياطي -->
                <div id="backup-section" class="dashboard-section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">💾 إدارة النسخ الاحتياطي</h2>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- تصدير البيانات -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">📤 تصدير البيانات</h3>
                            <div class="space-y-4">
                                <p class="text-sm text-gray-600">
                                    قم بتصدير جميع بيانات النظام في ملف JSON يمكن استيراده لاحقاً
                                </p>
                                <button
                                    onclick="exportAllData()"
                                    class="w-full touch-button px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                                >
                                    📥 تصدير جميع البيانات
                                </button>
                                <button
                                    onclick="exportAgentsOnly()"
                                    class="w-full touch-button px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                                >
                                    🏢 تصدير الوكلاء فقط
                                </button>
                                <button
                                    onclick="exportUsersOnly()"
                                    class="w-full touch-button px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                                >
                                    👥 تصدير المستخدمين فقط
                                </button>
                            </div>
                        </div>

                        <!-- استيراد البيانات -->
                        <div class="bg-white p-6 rounded-lg shadow-sm border">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">📥 استيراد البيانات</h3>
                            <div class="space-y-4">
                                <p class="text-sm text-gray-600">
                                    استيراد البيانات من ملف نسخة احتياطية سابقة
                                </p>
                                <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                                    <input
                                        type="file"
                                        id="importFile"
                                        accept=".json"
                                        class="hidden"
                                        onchange="handleFileImport(this.files[0])"
                                    >
                                    <button
                                        onclick="document.getElementById('importFile').click()"
                                        class="touch-button px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                                    >
                                        📁 اختر ملف للاستيراد
                                    </button>
                                    <p class="text-xs text-gray-500 mt-2">JSON files only</p>
                                </div>

                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <svg class="w-5 h-5 text-yellow-600 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                        <span class="text-yellow-800 text-sm font-medium">تحذير</span>
                                    </div>
                                    <p class="text-yellow-700 text-sm mt-1">
                                        سيتم استبدال جميع البيانات الحالية بالبيانات المستوردة
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- سجل الأنشطة -->
                <div id="logs-section" class="dashboard-section hidden">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">📋 سجل الأنشطة</h2>

                    <div class="bg-white rounded-lg shadow-sm border">
                        <div class="p-6 border-b">
                            <div class="flex items-center justify-between">
                                <h3 class="text-lg font-semibold text-gray-900">الأنشطة الأخيرة</h3>
                                <div class="flex space-x-2 space-x-reverse">
                                    <button
                                        onclick="refreshActivityLog()"
                                        class="touch-button px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                                    >
                                        🔄 تحديث
                                    </button>
                                    <button
                                        onclick="clearActivityLog()"
                                        class="touch-button px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
                                    >
                                        🗑️ مسح السجل
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="max-h-96 overflow-y-auto">
                            <div id="activityLogContainer" class="p-6">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تعديل الوكيل -->
    <div id="editAgentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-auto max-h-90vh overflow-y-auto">
            <!-- رأس النافذة -->
            <div class="flex items-center justify-between p-6 border-b bg-blue-50">
                <h2 class="text-xl font-bold text-blue-900">✏️ تعديل بيانات الوكيل</h2>
                <button onclick="closeEditAgentModal()" class="touch-button p-2 rounded-lg hover:bg-blue-100 transition-colors">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- محتوى النافذة -->
            <div class="p-6">
                <form id="editAgentForm" onsubmit="handleEditAgent(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- اسم الوكيل -->
                        <div class="md:col-span-2">
                            <label for="editAgentName" class="block text-sm font-medium text-gray-700 mb-2">
                                🏢 اسم الوكيل
                            </label>
                            <input
                                type="text"
                                id="editAgentName"
                                name="name"
                                placeholder="أدخل اسم الوكيل"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-right"
                                required
                            >
                        </div>

                        <!-- المدينة -->
                        <div>
                            <label for="editAgentCity" class="block text-sm font-medium text-gray-700 mb-2">
                                🏙️ المدينة
                            </label>
                            <select
                                id="editAgentCity"
                                name="city"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-right"
                                required
                            >
                                <option value="">اختر المدينة</option>
                                <option value="طرابلس">طرابلس</option>
                                <option value="بنغازي">بنغازي</option>
                                <option value="مصراتة">مصراتة</option>
                                <option value="سبها">سبها</option>
                                <option value="الزاوية">الزاوية</option>
                                <option value="البيضاء">البيضاء</option>
                                <option value="درنة">درنة</option>
                                <option value="زليتن">زليتن</option>
                                <option value="أجدابيا">أجدابيا</option>
                                <option value="غريان">غريان</option>
                            </select>
                        </div>

                        <!-- رقم الهاتف -->
                        <div>
                            <label for="editAgentPhone" class="block text-sm font-medium text-gray-700 mb-2">
                                📱 رقم الهاتف
                            </label>
                            <input
                                type="tel"
                                id="editAgentPhone"
                                name="phone"
                                placeholder="+218 XX XXX XXXX"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-right"
                                required
                                pattern="^\+218[0-9\s]{9,}$"
                                title="يرجى إدخال رقم هاتف ليبي صحيح"
                            >
                        </div>

                        <!-- العنوان -->
                        <div class="md:col-span-2">
                            <label for="editAgentAddress" class="block text-sm font-medium text-gray-700 mb-2">
                                📍 العنوان
                            </label>
                            <textarea
                                id="editAgentAddress"
                                name="address"
                                rows="3"
                                placeholder="أدخل العنوان التفصيلي"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-right"
                                required
                            ></textarea>
                        </div>

                        <!-- التقييم -->
                        <div>
                            <label for="editAgentRating" class="block text-sm font-medium text-gray-700 mb-2">
                                ⭐ التقييم
                            </label>
                            <select
                                id="editAgentRating"
                                name="rating"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-right"
                                required
                            >
                                <option value="">اختر التقييم</option>
                                <option value="5.0">⭐⭐⭐⭐⭐ (5.0)</option>
                                <option value="4.9">⭐⭐⭐⭐⭐ (4.9)</option>
                                <option value="4.8">⭐⭐⭐⭐⭐ (4.8)</option>
                                <option value="4.7">⭐⭐⭐⭐⭐ (4.7)</option>
                                <option value="4.6">⭐⭐⭐⭐⭐ (4.6)</option>
                                <option value="4.5">⭐⭐⭐⭐⭐ (4.5)</option>
                                <option value="4.4">⭐⭐⭐⭐⭐ (4.4)</option>
                                <option value="4.3">⭐⭐⭐⭐⭐ (4.3)</option>
                                <option value="4.2">⭐⭐⭐⭐⭐ (4.2)</option>
                                <option value="4.1">⭐⭐⭐⭐⭐ (4.1)</option>
                                <option value="4.0">⭐⭐⭐⭐☆ (4.0)</option>
                            </select>
                        </div>

                        <!-- الحالة -->
                        <div>
                            <label for="editAgentStatus" class="block text-sm font-medium text-gray-700 mb-2">
                                🔄 الحالة
                            </label>
                            <select
                                id="editAgentStatus"
                                name="status"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-right"
                                required
                            >
                                <option value="نشط">✅ نشط</option>
                                <option value="معطل">❌ معطل</option>
                                <option value="قيد المراجعة">⏳ قيد المراجعة</option>
                            </select>
                        </div>
                    </div>

                    <!-- أزرار العمل -->
                    <div class="flex justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t">
                        <button
                            type="button"
                            onclick="closeEditAgentModal()"
                            class="touch-button px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                        >
                            إلغاء
                        </button>
                        <button
                            type="submit"
                            class="touch-button px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                            💾 حفظ التغييرات
                        </button>
                    </div>
                </form>

                <!-- رسائل الحالة -->
                <div id="editAgentMessage" class="hidden mt-4 p-3 rounded-lg text-sm text-center"></div>
            </div>
        </div>
    </div>

    <!-- نافذة تأكيد الحذف -->
    <div id="deleteConfirmModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-md mx-auto">
            <!-- رأس النافذة -->
            <div class="flex items-center justify-between p-6 border-b bg-red-50">
                <h2 class="text-xl font-bold text-red-900">🗑️ تأكيد الحذف</h2>
                <button onclick="closeDeleteConfirmModal()" class="touch-button p-2 rounded-lg hover:bg-red-100 transition-colors">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- محتوى النافذة -->
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center ml-4">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">هل أنت متأكد؟</h3>
                        <p class="text-sm text-gray-600">هذا الإجراء لا يمكن التراجع عنه</p>
                    </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <p class="text-sm text-yellow-800">
                        <strong>⚠️ تحذير:</strong> سيتم حذف الوكيل نهائياً من النظام
                    </p>
                    <p id="deleteAgentInfo" class="text-sm text-gray-700 mt-2 font-medium"></p>
                </div>

                <!-- أزرار العمل -->
                <div class="flex justify-end space-x-3 space-x-reverse">
                    <button
                        onclick="closeDeleteConfirmModal()"
                        class="touch-button px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                    >
                        إلغاء
                    </button>
                    <button
                        id="confirmDeleteBtn"
                        class="touch-button px-6 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                    >
                        🗑️ حذف نهائي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة وكيل جديد -->
    <div id="addAgentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
        <div class="bg-white rounded-lg shadow-xl w-full max-w-2xl mx-auto max-h-90vh overflow-y-auto">
            <!-- رأس النافذة -->
            <div class="flex items-center justify-between p-6 border-b bg-green-50">
                <h2 class="text-xl font-bold text-green-900">➕ إضافة وكيل جديد</h2>
                <button onclick="closeAddAgentModal()" class="touch-button p-2 rounded-lg hover:bg-green-100 transition-colors">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <!-- محتوى النافذة -->
            <div class="p-6">
                <form id="addAgentForm" onsubmit="handleAddAgent(event)">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- اسم الوكيل -->
                        <div class="md:col-span-2">
                            <label for="addAgentName" class="block text-sm font-medium text-gray-700 mb-2">
                                🏢 اسم الوكيل *
                            </label>
                            <input
                                type="text"
                                id="addAgentName"
                                name="name"
                                placeholder="أدخل اسم الوكيل"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                required
                            >
                        </div>

                        <!-- المدينة -->
                        <div>
                            <label for="addAgentCity" class="block text-sm font-medium text-gray-700 mb-2">
                                🏙️ المدينة *
                            </label>
                            <select
                                id="addAgentCity"
                                name="city"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                required
                                onchange="updateCoordinates(this.value)"
                            >
                                <option value="">اختر المدينة</option>
                                <option value="طرابلس" data-lat="32.8872" data-lng="13.1913">طرابلس</option>
                                <option value="بنغازي" data-lat="32.1165" data-lng="20.0686">بنغازي</option>
                                <option value="مصراتة" data-lat="32.3745" data-lng="15.0919">مصراتة</option>
                                <option value="سبها" data-lat="27.0377" data-lng="14.4283">سبها</option>
                                <option value="الزاوية" data-lat="32.7569" data-lng="12.7278">الزاوية</option>
                                <option value="البيضاء" data-lat="32.7617" data-lng="21.7578">البيضاء</option>
                                <option value="درنة" data-lat="32.7569" data-lng="22.6367">درنة</option>
                                <option value="زليتن" data-lat="32.4675" data-lng="14.5569">زليتن</option>
                                <option value="أجدابيا" data-lat="30.7554" data-lng="20.2263">أجدابيا</option>
                                <option value="غريان" data-lat="32.1667" data-lng="13.0167">غريان</option>
                            </select>
                        </div>

                        <!-- رقم الهاتف -->
                        <div>
                            <label for="addAgentPhone" class="block text-sm font-medium text-gray-700 mb-2">
                                📱 رقم الهاتف *
                            </label>
                            <input
                                type="tel"
                                id="addAgentPhone"
                                name="phone"
                                placeholder="+218 XX XXX XXXX"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                required
                                pattern="^\+218[0-9\s]{9,}$"
                                title="يرجى إدخال رقم هاتف ليبي صحيح"
                            >
                        </div>

                        <!-- العنوان -->
                        <div class="md:col-span-2">
                            <label for="addAgentAddress" class="block text-sm font-medium text-gray-700 mb-2">
                                📍 العنوان *
                            </label>
                            <textarea
                                id="addAgentAddress"
                                name="address"
                                rows="3"
                                placeholder="أدخل العنوان التفصيلي"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                required
                            ></textarea>
                        </div>

                        <!-- الإحداثيات -->
                        <div>
                            <label for="addAgentLat" class="block text-sm font-medium text-gray-700 mb-2">
                                🌍 خط العرض
                            </label>
                            <input
                                type="number"
                                id="addAgentLat"
                                name="lat"
                                step="0.0001"
                                placeholder="32.8872"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                readonly
                            >
                        </div>

                        <div>
                            <label for="addAgentLng" class="block text-sm font-medium text-gray-700 mb-2">
                                🌍 خط الطول
                            </label>
                            <input
                                type="number"
                                id="addAgentLng"
                                name="lng"
                                step="0.0001"
                                placeholder="13.1913"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                readonly
                            >
                        </div>

                        <!-- التقييم الأولي -->
                        <div>
                            <label for="addAgentRating" class="block text-sm font-medium text-gray-700 mb-2">
                                ⭐ التقييم الأولي
                            </label>
                            <select
                                id="addAgentRating"
                                name="rating"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                required
                            >
                                <option value="4.0">⭐⭐⭐⭐☆ (4.0)</option>
                                <option value="4.1">⭐⭐⭐⭐⭐ (4.1)</option>
                                <option value="4.2">⭐⭐⭐⭐⭐ (4.2)</option>
                                <option value="4.3">⭐⭐⭐⭐⭐ (4.3)</option>
                                <option value="4.4">⭐⭐⭐⭐⭐ (4.4)</option>
                                <option value="4.5" selected>⭐⭐⭐⭐⭐ (4.5)</option>
                                <option value="4.6">⭐⭐⭐⭐⭐ (4.6)</option>
                                <option value="4.7">⭐⭐⭐⭐⭐ (4.7)</option>
                                <option value="4.8">⭐⭐⭐⭐⭐ (4.8)</option>
                                <option value="4.9">⭐⭐⭐⭐⭐ (4.9)</option>
                                <option value="5.0">⭐⭐⭐⭐⭐ (5.0)</option>
                            </select>
                        </div>

                        <!-- الحالة -->
                        <div>
                            <label for="addAgentStatus" class="block text-sm font-medium text-gray-700 mb-2">
                                🔄 الحالة
                            </label>
                            <select
                                id="addAgentStatus"
                                name="status"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors text-right"
                                required
                            >
                                <option value="نشط" selected>✅ نشط</option>
                                <option value="قيد المراجعة">⏳ قيد المراجعة</option>
                                <option value="معطل">❌ معطل</option>
                            </select>
                        </div>
                    </div>

                    <!-- أزرار العمل -->
                    <div class="flex justify-end space-x-3 space-x-reverse mt-6 pt-6 border-t">
                        <button
                            type="button"
                            onclick="closeAddAgentModal()"
                            class="touch-button px-6 py-3 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                        >
                            إلغاء
                        </button>
                        <button
                            type="submit"
                            class="touch-button px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                        >
                            ➕ إضافة الوكيل
                        </button>
                    </div>
                </form>

                <!-- رسائل الحالة -->
                <div id="addAgentMessage" class="hidden mt-4 p-3 rounded-lg text-sm text-center"></div>
            </div>
        </div>
    </div>

    <script>
        // بيانات الوكلاء
        const agents = [
            { name: 'وكيل طرابلس المركزي', lat: 32.8872, lng: 13.1913, address: 'شارع عمر المختار، وسط البلد', phone: '+218 21 234 5678', city: 'طرابلس' },
            { name: 'وكيل بنغازي الشرقي', lat: 32.1165, lng: 20.0686, address: 'شارع جمال عبد الناصر، حي الصابري', phone: '+218 61 234 5678', city: 'بنغازي' },
            { name: 'وكيل مصراتة التجاري', lat: 32.3745, lng: 15.0919, address: 'شارع طرابلس، المنطقة التجارية', phone: '+218 51 334 5678', city: 'مصراتة' },
            { name: 'وكيل سبها الجنوبي', lat: 27.0377, lng: 14.4283, address: 'شارع الجمهورية، وسط المدينة', phone: '+218 71 334 5678', city: 'سبها' },
            { name: 'وكيل الزاوية الغربي', lat: 32.7569, lng: 12.7278, address: 'شارع الشهداء، حي النصر', phone: '+218 23 334 5678', city: 'الزاوية' }
        ];

        let map;
        let markers = [];

        // تهيئة الخريطة
        function initMap() {
            const libya = { lat: 32.8872, lng: 13.1913 };
            const isMobile = window.innerWidth < 768;

            map = new google.maps.Map(document.getElementById('mapContainer'), {
                zoom: isMobile ? 5 : 6,
                center: libya,
                restriction: {
                    latLngBounds: {
                        north: 33.5,
                        south: 19.5,
                        east: 25.5,
                        west: 9.5
                    }
                },
                gestureHandling: isMobile ? 'cooperative' : 'auto',
                zoomControl: true,
                mapTypeControl: !isMobile,
                scaleControl: false,
                streetViewControl: false,
                rotateControl: false,
                fullscreenControl: !isMobile,
                styles: [
                    {
                        featureType: 'water',
                        elementType: 'geometry',
                        stylers: [{ color: '#e9e9e9' }, { lightness: 17 }]
                    },
                    {
                        featureType: 'poi',
                        elementType: 'labels',
                        stylers: [{ visibility: isMobile ? 'off' : 'on' }]
                    }
                ]
            });

            // إضافة علامات للوكلاء
            agents.forEach((agent, index) => {
                const marker = new google.maps.Marker({
                    position: { lat: agent.lat, lng: agent.lng },
                    map: map,
                    title: agent.name,
                    animation: google.maps.Animation.DROP,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
                            <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="20" cy="20" r="18" fill="#3B82F6" stroke="#1E40AF" stroke-width="2"/>
                                <text x="20" y="26" text-anchor="middle" fill="white" font-size="16" font-family="Arial">🏢</text>
                            </svg>
                        `),
                        scaledSize: new google.maps.Size(40, 40)
                    }
                });

                const infoWindow = new google.maps.InfoWindow({
                    content: `
                        <div style="direction: rtl; font-family: Arial; padding: 15px; max-width: 300px;">
                            <h3 style="margin: 0 0 10px 0; color: #1E40AF; font-size: 18px;">${agent.name}</h3>
                            <p style="margin: 5px 0; color: #666; font-size: 14px;">📍 ${agent.address}</p>
                            <p style="margin: 5px 0; color: #059669; font-weight: bold; font-size: 14px;">📞 ${agent.phone}</p>
                            <div style="margin-top: 15px; display: flex; gap: 10px;">
                                <button onclick="window.open('tel:${agent.phone}')" style="background: #3B82F6; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">
                                    📞 اتصل
                                </button>
                                <button onclick="getDirections(${agent.lat}, ${agent.lng})" style="background: #059669; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 12px;">
                                    🧭 اتجاهات
                                </button>
                            </div>
                        </div>
                    `
                });

                marker.addListener('click', () => {
                    // إغلاق جميع النوافذ المفتوحة
                    markers.forEach(m => m.infoWindow.close());
                    // فتح النافذة الحالية
                    infoWindow.open(map, marker);
                });

                markers.push({ marker, infoWindow, agent });
            });
        }

        // عرض تفاصيل الوكيل
        function showAgentDetails(city) {
            const agent = agents.find(a => a.city === city);
            if (agent) {
                map.setCenter({ lat: agent.lat, lng: agent.lng });
                map.setZoom(12);
                
                const marker = markers.find(m => m.agent.city === city);
                if (marker) {
                    marker.infoWindow.open(map, marker.marker);
                }
            }
        }

        // البحث عن أقرب وكيل
        function findNearestAgent() {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(position => {
                    const userLat = position.coords.latitude;
                    const userLng = position.coords.longitude;
                    
                    let nearest = null;
                    let minDistance = Infinity;
                    
                    agents.forEach(agent => {
                        const distance = calculateDistance(userLat, userLng, agent.lat, agent.lng);
                        if (distance < minDistance) {
                            minDistance = distance;
                            nearest = agent;
                        }
                    });
                    
                    if (nearest) {
                        alert(`أقرب وكيل لك هو: ${nearest.name}\nالمسافة: ${minDistance.toFixed(1)} كم\nالهاتف: ${nearest.phone}`);
                        showAgentDetails(nearest.city);
                    }
                }, () => {
                    alert('تعذر تحديد موقعك. يرجى السماح بالوصول للموقع.');
                });
            } else {
                alert('متصفحك لا يدعم تحديد الموقع.');
            }
        }

        // الحصول على الاتجاهات
        function getDirections(lat, lng) {
            const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}`;
            window.open(url, '_blank');
        }

        // حساب المسافة
        function calculateDistance(lat1, lng1, lat2, lng2) {
            const R = 6371;
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLng = (lng2 - lng1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLng/2) * Math.sin(dLng/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        // متغيرات النظام
        let currentAdmin = null;
        let isAdminLoggedIn = false;
        let systemSettings = {
            siteName: 'نقطة قريبة',
            siteDescription: 'أقرب وكيل لك في ليبيا',
            adminPassword: 'admin123',
            maintenanceMode: false,
            allowRegistration: false,
            maxAgents: 50,
            autoBackup: true,
            emailNotifications: true
        };

        // نظام إدارة البيانات المحلي
        class DataManager {
            constructor() {
                this.storageKey = 'alwatan_admin_data';
                this.loadData();
            }

            // تحميل البيانات من localStorage
            loadData() {
                try {
                    const savedData = localStorage.getItem(this.storageKey);
                    if (savedData) {
                        const data = JSON.parse(savedData);
                        agentsData = data.agents || this.getDefaultAgents();
                        usersData = data.users || this.getDefaultUsers();
                        systemSettings = { ...systemSettings, ...data.settings };
                        this.logActivity('تم تحميل البيانات من التخزين المحلي');
                    } else {
                        agentsData = this.getDefaultAgents();
                        usersData = this.getDefaultUsers();
                        this.saveData();
                    }
                } catch (error) {
                    console.error('خطأ في تحميل البيانات:', error);
                    agentsData = this.getDefaultAgents();
                    usersData = this.getDefaultUsers();
                }
            }

            // حفظ البيانات في localStorage
            saveData() {
                try {
                    const dataToSave = {
                        agents: agentsData,
                        users: usersData,
                        settings: systemSettings,
                        lastUpdate: new Date().toISOString()
                    };
                    localStorage.setItem(this.storageKey, JSON.stringify(dataToSave));
                    this.showSaveIndicator();
                    return true;
                } catch (error) {
                    console.error('خطأ في حفظ البيانات:', error);
                    this.showErrorIndicator('فشل في حفظ البيانات');
                    return false;
                }
            }

            // البيانات الافتراضية للوكلاء
            getDefaultAgents() {
                return [
                    { id: 1, name: 'وكيل طرابلس المركزي', city: 'طرابلس', phone: '+218 21 234 5678', rating: 4.8, status: 'نشط', address: 'شارع عمر المختار، وسط البلد', lat: 32.8872, lng: 13.1913, createdAt: '2024-01-01' },
                    { id: 2, name: 'وكيل بنغازي الشرقي', city: 'بنغازي', phone: '+218 61 234 5678', rating: 4.7, status: 'نشط', address: 'شارع جمال عبد الناصر، حي الصابري', lat: 32.1165, lng: 20.0686, createdAt: '2024-01-02' },
                    { id: 3, name: 'وكيل مصراتة التجاري', city: 'مصراتة', phone: '+218 51 334 5678', rating: 4.6, status: 'نشط', address: 'شارع طرابلس، المنطقة التجارية', lat: 32.3745, lng: 15.0919, createdAt: '2024-01-03' },
                    { id: 4, name: 'وكيل سبها الجنوبي', city: 'سبها', phone: '+218 71 334 5678', rating: 4.5, status: 'نشط', address: 'شارع الجمهورية، وسط المدينة', lat: 27.0377, lng: 14.4283, createdAt: '2024-01-04' },
                    { id: 5, name: 'وكيل الزاوية الغربي', city: 'الزاوية', phone: '+218 23 334 5678', rating: 4.4, status: 'نشط', address: 'شارع الشهداء، حي النصر', lat: 32.7569, lng: 12.7278, createdAt: '2024-01-05' }
                ];
            }

            // البيانات الافتراضية للمستخدمين
            getDefaultUsers() {
                return [
                    { id: 1, name: 'أحمد محمد', contact: '+218 91 123 4567', type: 'مستخدم', joinDate: '2024-01-15', status: 'نشط', lastLogin: '2024-06-17', permissions: ['view'] },
                    { id: 2, name: 'فاطمة علي', contact: '<EMAIL>', type: 'وكيل', joinDate: '2024-02-20', status: 'نشط', lastLogin: '2024-06-16', permissions: ['view', 'edit_own'] },
                    { id: 3, name: 'محمد أحمد', contact: '+218 92 987 6543', type: 'مستخدم', joinDate: '2024-03-10', status: 'معطل', lastLogin: '2024-06-10', permissions: ['view'] },
                    { id: 4, name: 'سارة محمود', contact: '+218 93 456 7890', type: 'مدير فرعي', joinDate: '2024-03-01', status: 'نشط', lastLogin: '2024-06-17', permissions: ['view', 'edit', 'delete'] }
                ];
            }

            // تسجيل الأنشطة
            logActivity(activity) {
                const timestamp = new Date().toLocaleString('ar-LY');
                console.log(`[${timestamp}] ${activity}`);

                // حفظ في سجل الأنشطة
                let activities = JSON.parse(localStorage.getItem('alwatan_activities') || '[]');
                activities.unshift({
                    id: Date.now(),
                    activity,
                    timestamp,
                    admin: currentAdmin?.name || 'النظام'
                });

                // الاحتفاظ بآخر 100 نشاط فقط
                if (activities.length > 100) {
                    activities = activities.slice(0, 100);
                }

                localStorage.setItem('alwatan_activities', JSON.stringify(activities));
            }

            // مؤشر الحفظ
            showSaveIndicator() {
                const indicator = document.getElementById('saveIndicator') || this.createSaveIndicator();
                indicator.className = 'fixed top-4 left-4 bg-green-100 border border-green-400 text-green-700 px-3 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
                indicator.innerHTML = '💾 تم الحفظ';

                setTimeout(() => {
                    indicator.className = indicator.className.replace('bg-green-100 border-green-400 text-green-700', 'bg-gray-100 border-gray-300 text-gray-500');
                }, 2000);
            }

            // مؤشر الخطأ
            showErrorIndicator(message) {
                const indicator = document.getElementById('saveIndicator') || this.createSaveIndicator();
                indicator.className = 'fixed top-4 left-4 bg-red-100 border border-red-400 text-red-700 px-3 py-2 rounded-lg shadow-lg z-50';
                indicator.innerHTML = `❌ ${message}`;

                setTimeout(() => {
                    indicator.className = indicator.className.replace('bg-red-100 border-red-400 text-red-700', 'bg-gray-100 border-gray-300 text-gray-500');
                }, 3000);
            }

            // إنشاء مؤشر الحفظ
            createSaveIndicator() {
                const indicator = document.createElement('div');
                indicator.id = 'saveIndicator';
                indicator.className = 'fixed top-4 left-4 bg-gray-100 border border-gray-300 text-gray-500 px-3 py-2 rounded-lg shadow-lg z-50';
                indicator.innerHTML = '💾 جاهز';
                document.body.appendChild(indicator);
                return indicator;
            }

            // نسخ احتياطي
            exportData() {
                const data = {
                    agents: agentsData,
                    users: usersData,
                    settings: systemSettings,
                    exportDate: new Date().toISOString(),
                    version: '1.0'
                };

                const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `alwatan-backup-${new Date().toISOString().split('T')[0]}.json`;
                a.click();
                URL.revokeObjectURL(url);

                this.logActivity('تم تصدير نسخة احتياطية من البيانات');
            }

            // استيراد البيانات
            importData(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);
                            if (data.agents && data.users && data.settings) {
                                agentsData = data.agents;
                                usersData = data.users;
                                systemSettings = { ...systemSettings, ...data.settings };
                                this.saveData();
                                this.logActivity('تم استيراد البيانات من ملف خارجي');
                                resolve(true);
                            } else {
                                reject('تنسيق الملف غير صحيح');
                            }
                        } catch (error) {
                            reject('خطأ في قراءة الملف');
                        }
                    };
                    reader.readAsText(file);
                });
            }
        }

        // إنشاء مثيل من مدير البيانات
        const dataManager = new DataManager();

        // وظائف تسجيل دخول المدير
        function showAdminLogin() {
            document.getElementById('adminLoginModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeAdminLogin() {
            document.getElementById('adminLoginModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            clearAdminLoginForm();
        }

        function clearAdminLoginForm() {
            document.getElementById('adminLoginForm').reset();
            hideAdminLoginMessage();
        }

        function toggleAdminPassword() {
            const passwordInput = document.getElementById('adminPassword');
            const eyeIcon = document.getElementById('adminEyeIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
                `;
            } else {
                passwordInput.type = 'password';
                eyeIcon.innerHTML = `
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                `;
            }
        }

        function showAdminLoginMessage(message, type = 'info') {
            const messageDiv = document.getElementById('adminLoginMessage');
            messageDiv.className = `mt-4 p-3 rounded-lg text-sm text-center ${
                type === 'success' ? 'bg-green-100 text-green-800' :
                type === 'error' ? 'bg-red-100 text-red-800' :
                'bg-blue-100 text-blue-800'
            }`;
            messageDiv.textContent = message;
            messageDiv.classList.remove('hidden');
        }

        function hideAdminLoginMessage() {
            document.getElementById('adminLoginMessage').classList.add('hidden');
        }

        function handleAdminLogin(event) {
            event.preventDefault();

            const username = document.getElementById('adminUsername').value;
            const password = document.getElementById('adminPassword').value;

            if (!username || !password) {
                showAdminLoginMessage('يرجى ملء جميع الحقول', 'error');
                return;
            }

            showAdminLoginMessage('جاري التحقق من البيانات...', 'info');

            setTimeout(() => {
                // بيانات المدير
                if (username === 'admin' && password === 'admin123') {
                    currentAdmin = { username: 'admin', name: 'مدير النظام' };
                    isAdminLoggedIn = true;

                    showAdminLoginMessage('تم تسجيل الدخول بنجاح! 🎉', 'success');

                    setTimeout(() => {
                        closeAdminLogin();
                        showAdminDashboard();
                    }, 1500);
                } else {
                    showAdminLoginMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
                }
            }, 1000);
        }

        function showAdminDashboard() {
            document.getElementById('adminDashboard').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            document.getElementById('adminName').textContent = currentAdmin.name;
            loadDashboardData();

            // تفعيل وضع المدير لرفع الصور
            enableAdminImageMode();
        }

        function enableAdminImageMode() {
            // إظهار شارات المدير
            const adminBadge = document.getElementById('adminBadge');
            const adminBadgeMobile = document.getElementById('adminBadgeMobile');
            if (adminBadge) adminBadge.classList.remove('hidden');
            if (adminBadgeMobile) adminBadgeMobile.classList.remove('hidden');

            // إظهار أزرار التحكم
            const adminControls = document.getElementById('adminImageControls');
            const adminControlsMobile = document.getElementById('adminImageControlsMobile');
            if (adminControls) adminControls.classList.remove('hidden');
            if (adminControlsMobile) adminControlsMobile.classList.remove('hidden');

            // تفعيل وضع المدير لمنطقة الصورة
            const imageContainers = document.querySelectorAll('.office-image-container');
            imageContainers.forEach(container => {
                container.classList.add('admin-mode');
            });

            // إظهار overlay للتحكم
            const overlays = document.querySelectorAll('.image-upload-overlay');
            overlays.forEach(overlay => {
                overlay.style.display = 'flex';
            });

            // تحديث النصوص التوضيحية
            const instructions = document.getElementById('imageInstructions');
            const instructionsMobile = document.getElementById('imageInstructionsMobile');

            if (instructions) {
                instructions.innerHTML = `
                    <p class="text-gray-600 font-medium">انقر لإضافة صورة المكتب</p>
                    <p class="text-gray-500 text-sm mt-2">JPG, PNG, GIF (حد أقصى 5MB)</p>
                `;
            }

            if (instructionsMobile) {
                instructionsMobile.innerHTML = `
                    <p class="text-gray-600 text-sm">انقر لإضافة صورة</p>
                    <p class="text-gray-500 text-xs mt-1">JPG, PNG, GIF</p>
                `;
            }
        }

        function disableAdminImageMode() {
            // إخفاء شارات المدير
            const adminBadge = document.getElementById('adminBadge');
            const adminBadgeMobile = document.getElementById('adminBadgeMobile');
            if (adminBadge) adminBadge.classList.add('hidden');
            if (adminBadgeMobile) adminBadgeMobile.classList.add('hidden');

            // إخفاء أزرار التحكم
            const adminControls = document.getElementById('adminImageControls');
            const adminControlsMobile = document.getElementById('adminImageControlsMobile');
            if (adminControls) adminControls.classList.add('hidden');
            if (adminControlsMobile) adminControlsMobile.classList.add('hidden');

            // إلغاء وضع المدير لمنطقة الصورة
            const imageContainers = document.querySelectorAll('.office-image-container');
            imageContainers.forEach(container => {
                container.classList.remove('admin-mode');
            });

            // إخفاء overlay للتحكم
            const overlays = document.querySelectorAll('.image-upload-overlay');
            overlays.forEach(overlay => {
                overlay.style.display = 'none';
            });

            // إعادة تعيين النصوص التوضيحية
            const instructions = document.getElementById('imageInstructions');
            const instructionsMobile = document.getElementById('imageInstructionsMobile');

            if (instructions) {
                instructions.innerHTML = `
                    <p class="text-gray-600 font-medium">صورة المكتب الرئيسي</p>
                    <p class="text-gray-500 text-sm mt-2">يمكن للمدير فقط تعديل الصورة</p>
                `;
            }

            if (instructionsMobile) {
                instructionsMobile.innerHTML = `
                    <p class="text-gray-600 text-sm">صورة المكتب الرئيسي</p>
                    <p class="text-gray-500 text-xs mt-1">للمدير فقط</p>
                `;
            }
        }

        function logoutAdmin() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                currentAdmin = null;
                isAdminLoggedIn = false;
                document.getElementById('adminDashboard').classList.add('hidden');
                document.body.style.overflow = 'auto';
                showAdminLoginMessage('تم تسجيل الخروج بنجاح', 'success');

                // إلغاء وضع المدير لرفع الصور
                disableAdminImageMode();
            }
        }

        function showDashboardSection(section) {
            // إخفاء جميع الأقسام
            document.querySelectorAll('.dashboard-section').forEach(el => {
                el.classList.add('hidden');
            });

            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.dashboard-nav-btn').forEach(btn => {
                btn.classList.remove('active', 'bg-blue-100', 'text-blue-700');
            });

            // إظهار القسم المحدد
            document.getElementById(section + '-section').classList.remove('hidden');

            // تفعيل الزر المحدد
            event.target.classList.add('active', 'bg-blue-100', 'text-blue-700');

            // تحميل البيانات حسب القسم
            if (section === 'agents') {
                loadAgentsTable();
            } else if (section === 'users') {
                loadUsersTable();
            } else if (section === 'system') {
                loadSystemSettings();
            } else if (section === 'backup') {
                updateSystemStats();
            } else if (section === 'logs') {
                loadActivityLog();
            }
        }

        function loadDashboardData() {
            // تحديث الإحصائيات
            document.getElementById('totalAgents').textContent = agentsData.length;
            document.getElementById('activeUsers').textContent = usersData.filter(u => u.status === 'نشط').length;

            // تحميل جدول الوكلاء إذا كان القسم مفتوح
            loadAgentsTable();

            // تحديث إعدادات النظام إذا كان القسم مفتوح
            if (!document.getElementById('system-section').classList.contains('hidden')) {
                loadSystemSettings();
            }
        }

        // وظائف أدوات النظام
        function clearAllData() {
            if (confirm('⚠️ تحذير خطير!\n\nهل أنت متأكد من حذف جميع البيانات؟\nهذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد نهائي: سيتم حذف جميع الوكلاء والمستخدمين والإعدادات!')) {
                    localStorage.removeItem('alwatan_admin_data');
                    localStorage.removeItem('alwatan_activities');
                    localStorage.removeItem('alwatan_last_backup');

                    // إعادة تحميل البيانات الافتراضية
                    dataManager.loadData();

                    showSuccessNotification('تم مسح جميع البيانات وإعادة تعيين النظام');
                    loadDashboardData();
                    loadAgentsTable();
                    loadUsersTable();
                    loadSystemSettings();
                    dataManager.logActivity('تم مسح جميع البيانات وإعادة تعيين النظام');
                }
            }
        }

        function resetToDefaults() {
            if (confirm('هل تريد إعادة تعيين جميع الإعدادات للقيم الافتراضية؟')) {
                systemSettings = {
                    siteName: 'نقطة قريبة',
                    siteDescription: 'أقرب وكيل لك في ليبيا',
                    adminPassword: 'admin123',
                    maintenanceMode: false,
                    allowRegistration: false,
                    maxAgents: 50,
                    autoBackup: true,
                    emailNotifications: true
                };

                dataManager.saveData();
                loadSystemSettings();
                showSuccessNotification('تم إعادة تعيين الإعدادات للقيم الافتراضية');
                dataManager.logActivity('تم إعادة تعيين إعدادات النظام للقيم الافتراضية');
            }
        }

        function optimizeDatabase() {
            showSuccessNotification('جاري تحسين قاعدة البيانات...');

            setTimeout(() => {
                // تنظيف البيانات المكررة
                const uniqueAgents = agentsData.filter((agent, index, self) =>
                    index === self.findIndex(a => a.phone === agent.phone)
                );

                const uniqueUsers = usersData.filter((user, index, self) =>
                    index === self.findIndex(u => u.contact === user.contact)
                );

                agentsData.length = 0;
                agentsData.push(...uniqueAgents);

                usersData.length = 0;
                usersData.push(...uniqueUsers);

                // تنظيف سجل الأنشطة القديمة
                let activities = JSON.parse(localStorage.getItem('alwatan_activities') || '[]');
                activities = activities.slice(0, 50); // الاحتفاظ بآخر 50 نشاط فقط
                localStorage.setItem('alwatan_activities', JSON.stringify(activities));

                dataManager.saveData();
                loadDashboardData();
                loadAgentsTable();
                loadUsersTable();

                showSuccessNotification('تم تحسين قاعدة البيانات بنجاح');
                dataManager.logActivity('تم تحسين قاعدة البيانات وإزالة البيانات المكررة');
            }, 2000);
        }

        function generateReport() {
            const report = {
                reportDate: new Date().toISOString(),
                systemInfo: {
                    totalAgents: agentsData.length,
                    activeAgents: agentsData.filter(a => a.status === 'نشط').length,
                    totalUsers: usersData.length,
                    activeUsers: usersData.filter(u => u.status === 'نشط').length,
                    dataSize: formatBytes(new Blob([localStorage.getItem('alwatan_admin_data') || '']).size)
                },
                agentsByCity: {},
                usersByType: {},
                recentActivities: JSON.parse(localStorage.getItem('alwatan_activities') || '[]').slice(0, 10)
            };

            // تجميع الوكلاء حسب المدينة
            agentsData.forEach(agent => {
                report.agentsByCity[agent.city] = (report.agentsByCity[agent.city] || 0) + 1;
            });

            // تجميع المستخدمين حسب النوع
            usersData.forEach(user => {
                report.usersByType[user.type] = (report.usersByType[user.type] || 0) + 1;
            });

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `system-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);

            dataManager.logActivity('تم إنشاء تقرير شامل للنظام');
            showSuccessNotification('تم إنشاء التقرير وتحميله');
        }

        // وظائف سجل الأنشطة
        function refreshActivityLog() {
            loadActivityLog();
            showSuccessNotification('تم تحديث سجل الأنشطة');
        }

        function clearActivityLog() {
            if (confirm('هل تريد مسح جميع الأنشطة المسجلة؟')) {
                localStorage.removeItem('alwatan_activities');
                loadActivityLog();
                showSuccessNotification('تم مسح سجل الأنشطة');
                dataManager.logActivity('تم مسح سجل الأنشطة');
            }
        }

        function loadActivityLog() {
            const activities = JSON.parse(localStorage.getItem('alwatan_activities') || '[]');
            const container = document.getElementById('activityLogContainer');

            if (activities.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8 text-gray-500">
                        <svg class="w-12 h-12 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <p>لا توجد أنشطة مسجلة</p>
                    </div>
                `;
                return;
            }

            container.innerHTML = activities.map(activity => `
                <div class="flex items-start p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center ml-3 flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900">${activity.activity}</p>
                        <div class="flex items-center justify-between mt-1">
                            <span class="text-xs text-gray-500">بواسطة: ${activity.admin}</span>
                            <span class="text-xs text-gray-400">${activity.timestamp}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function loadAgentsTable() {
            const tbody = document.getElementById('agentsTableBody');
            if (!tbody) return;

            if (agentsData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <div class="text-4xl mb-3">🏢</div>
                                <p class="text-lg font-medium">لا توجد وكلاء مسجلين</p>
                                <p class="text-sm mt-1">انقر على "إضافة وكيل جديد" لبدء إضافة الوكلاء</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';
            agentsData.forEach(agent => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';
                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white text-sm font-bold ml-3">
                                ${agent.name.charAt(0)}
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">${agent.name}</div>
                                <div class="text-sm text-gray-500">${agent.address || 'لا يوجد عنوان'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${agent.city}</div>
                        <div class="text-sm text-gray-500">ليبيا</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${agent.phone}</div>
                        <a href="tel:${agent.phone}" class="text-sm text-blue-600 hover:text-blue-800">اتصل الآن</a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <span class="text-yellow-400 text-lg">⭐</span>
                            <span class="text-sm font-medium text-gray-900 mr-1">${agent.rating}</span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            agent.status === 'نشط' ? 'bg-green-100 text-green-800' :
                            agent.status === 'معطل' ? 'bg-red-100 text-red-800' :
                            'bg-yellow-100 text-yellow-800'
                        }">
                            ${agent.status === 'نشط' ? '✅ نشط' :
                              agent.status === 'معطل' ? '❌ معطل' :
                              '⏳ قيد المراجعة'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="editAgent(${agent.id})" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg hover:bg-blue-200 transition-colors">
                                ✏️ تعديل
                            </button>
                            <button onclick="deleteAgent(${agent.id})" class="bg-red-100 text-red-800 px-3 py-1 rounded-lg hover:bg-red-200 transition-colors">
                                🗑️ حذف
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        function loadUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            if (!tbody) return;

            if (usersData.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                            <div class="flex flex-col items-center">
                                <div class="text-4xl mb-3">👥</div>
                                <p class="text-lg font-medium">لا توجد مستخدمين مسجلين</p>
                                <p class="text-sm mt-1">انقر على "إضافة مستخدم جديد" لبدء إضافة المستخدمين</p>
                            </div>
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = '';
            usersData.forEach(user => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50 transition-colors';

                // تحديد أيقونة حسب نوع المستخدم
                const userIcon = user.type === 'مدير فرعي' ? '👑' :
                                user.type === 'وكيل' ? '🏢' : '👤';

                // تحديد لون الخلفية حسب نوع المستخدم
                const userBgColor = user.type === 'مدير فرعي' ? 'from-purple-500 to-pink-600' :
                                   user.type === 'وكيل' ? 'from-blue-500 to-indigo-600' :
                                   'from-gray-500 to-gray-600';

                row.innerHTML = `
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="w-12 h-12 rounded-full bg-gradient-to-r ${userBgColor} flex items-center justify-center text-white text-lg ml-3">
                                ${userIcon}
                            </div>
                            <div>
                                <div class="text-sm font-medium text-gray-900">${user.name}</div>
                                <div class="text-sm text-gray-500">انضم في ${user.joinDate}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${user.contact}</div>
                        ${user.contact.includes('@') ?
                            `<a href="mailto:${user.contact}" class="text-sm text-blue-600 hover:text-blue-800">إرسال بريد</a>` :
                            `<a href="tel:${user.contact}" class="text-sm text-blue-600 hover:text-blue-800">اتصل الآن</a>`
                        }
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.type === 'مدير فرعي' ? 'bg-purple-100 text-purple-800' :
                            user.type === 'وكيل' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                        }">
                            ${user.type}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">${user.lastLogin || 'لم يسجل دخول'}</div>
                        <div class="text-sm text-gray-500">آخر دخول</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.status === 'نشط' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }">
                            ${user.status === 'نشط' ? '✅ نشط' : '❌ معطل'}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2 space-x-reverse">
                            <button onclick="editUser(${user.id})" class="bg-blue-100 text-blue-800 px-3 py-1 rounded-lg hover:bg-blue-200 transition-colors">
                                ✏️ تعديل
                            </button>
                            <button onclick="toggleUserStatus(${user.id})" class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-lg hover:bg-yellow-200 transition-colors">
                                ${user.status === 'نشط' ? '⏸️ تعطيل' : '▶️ تفعيل'}
                            </button>
                            <button onclick="deleteUser(${user.id})" class="bg-red-100 text-red-800 px-3 py-1 rounded-lg hover:bg-red-200 transition-colors">
                                🗑️ حذف
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // متغيرات التعديل والحذف
        let currentEditingAgent = null;
        let currentDeletingAgent = null;

        function editAgent(id) {
            const agent = agentsData.find(a => a.id === id);
            if (agent) {
                currentEditingAgent = agent;
                showEditAgentModal(agent);
            }
        }

        function showEditAgentModal(agent) {
            // ملء البيانات في النموذج
            document.getElementById('editAgentName').value = agent.name;
            document.getElementById('editAgentCity').value = agent.city;
            document.getElementById('editAgentPhone').value = agent.phone;
            document.getElementById('editAgentAddress').value = agent.address;
            document.getElementById('editAgentRating').value = agent.rating;
            document.getElementById('editAgentStatus').value = agent.status;

            // إظهار النافذة
            document.getElementById('editAgentModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeEditAgentModal() {
            document.getElementById('editAgentModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('editAgentForm').reset();
            hideEditAgentMessage();
            currentEditingAgent = null;
        }

        function handleEditAgent(event) {
            event.preventDefault();

            if (!currentEditingAgent) {
                showEditAgentMessage('خطأ في تحديد الوكيل', 'error');
                return;
            }

            // الحصول على البيانات من النموذج
            const formData = new FormData(event.target);
            const updatedAgent = {
                id: currentEditingAgent.id,
                name: formData.get('name').trim(),
                city: formData.get('city'),
                phone: formData.get('phone').trim(),
                address: formData.get('address').trim(),
                rating: parseFloat(formData.get('rating')),
                status: formData.get('status')
            };

            // التحقق من صحة البيانات
            if (!updatedAgent.name || !updatedAgent.city || !updatedAgent.phone || !updatedAgent.address) {
                showEditAgentMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من تنسيق رقم الهاتف
            const phoneRegex = /^\+218[0-9\s]{9,}$/;
            if (!phoneRegex.test(updatedAgent.phone)) {
                showEditAgentMessage('يرجى إدخال رقم هاتف ليبي صحيح', 'error');
                return;
            }

            // التحقق من عدم تكرار رقم الهاتف
            const existingAgent = agentsData.find(a => a.id !== updatedAgent.id && a.phone === updatedAgent.phone);
            if (existingAgent) {
                showEditAgentMessage('رقم الهاتف مستخدم من قبل وكيل آخر', 'error');
                return;
            }

            showEditAgentMessage('جاري حفظ التغييرات...', 'info');

            // محاكاة عملية الحفظ
            setTimeout(() => {
                // تحديث البيانات
                const agentIndex = agentsData.findIndex(a => a.id === currentEditingAgent.id);
                if (agentIndex !== -1) {
                    agentsData[agentIndex] = updatedAgent;

                    showEditAgentMessage('تم حفظ التغييرات بنجاح! 🎉', 'success');

                    setTimeout(() => {
                        closeEditAgentModal();
                        loadAgentsTable();
                        loadDashboardData();
                        showSuccessNotification('تم تحديث بيانات الوكيل بنجاح');
                    }, 1500);
                } else {
                    showEditAgentMessage('خطأ في حفظ البيانات', 'error');
                }
            }, 1000);
        }

        function showEditAgentMessage(message, type = 'info') {
            const messageDiv = document.getElementById('editAgentMessage');
            messageDiv.className = `mt-4 p-3 rounded-lg text-sm text-center ${
                type === 'success' ? 'bg-green-100 text-green-800' :
                type === 'error' ? 'bg-red-100 text-red-800' :
                'bg-blue-100 text-blue-800'
            }`;
            messageDiv.textContent = message;
            messageDiv.classList.remove('hidden');
        }

        function hideEditAgentMessage() {
            document.getElementById('editAgentMessage').classList.add('hidden');
        }

        function deleteAgent(id) {
            const agent = agentsData.find(a => a.id === id);
            if (agent) {
                currentDeletingAgent = agent;
                showDeleteConfirmModal(agent);
            }
        }

        function showDeleteConfirmModal(agent) {
            document.getElementById('deleteAgentInfo').textContent = `الوكيل: ${agent.name} - ${agent.city}`;
            document.getElementById('deleteConfirmModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // ربط زر التأكيد
            document.getElementById('confirmDeleteBtn').onclick = () => confirmDeleteAgent();
        }

        function closeDeleteConfirmModal() {
            document.getElementById('deleteConfirmModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            currentDeletingAgent = null;
        }

        function confirmDeleteAgent() {
            if (!currentDeletingAgent) {
                return;
            }

            // تحديث زر التأكيد لإظهار التحميل
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            const originalText = confirmBtn.innerHTML;
            confirmBtn.innerHTML = '⏳ جاري الحذف...';
            confirmBtn.disabled = true;

            // محاكاة عملية الحذف
            setTimeout(() => {
                // حذف الوكيل من البيانات
                agentsData = agentsData.filter(a => a.id !== currentDeletingAgent.id);

                // إغلاق النافذة
                closeDeleteConfirmModal();

                // تحديث الجداول والإحصائيات
                loadAgentsTable();
                loadDashboardData();

                // إظهار رسالة نجاح
                showSuccessNotification(`تم حذف الوكيل "${currentDeletingAgent.name}" بنجاح`);

                // إعادة تعيين الزر
                confirmBtn.innerHTML = originalText;
                confirmBtn.disabled = false;
            }, 1500);
        }

        function showSuccessNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
            notification.innerHTML = `
                <div class="flex items-center">
                    <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm font-medium">${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 4 ثوان
            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        function editUser(id) {
            const user = usersData.find(u => u.id === id);
            if (user) {
                const newName = prompt('اسم المستخدم الجديد:', user.name);
                if (newName && newName.trim()) {
                    const newContact = prompt('معلومات الاتصال الجديدة:', user.contact);
                    if (newContact && newContact.trim()) {
                        const newType = prompt('نوع المستخدم (عميل/وكيل/مدير فرعي):', user.type);
                        if (newType && newType.trim()) {
                            // تحديث بيانات المستخدم
                            user.name = newName.trim();
                            user.contact = newContact.trim();
                            user.type = newType.trim();
                            user.lastModified = new Date().toISOString().split('T')[0];

                            // حفظ البيانات
                            dataManager.saveData();
                            dataManager.logActivity(`تم تعديل بيانات المستخدم: ${user.name}`);

                            // تحديث الجدول
                            loadUsersTable();
                            loadDashboardData();
                            showSuccessNotification(`تم تحديث بيانات المستخدم "${user.name}" بنجاح`);
                        }
                    }
                }
            }
        }

        function toggleUserStatus(id) {
            const user = usersData.find(u => u.id === id);
            if (user) {
                const newStatus = user.status === 'نشط' ? 'معطل' : 'نشط';
                const action = newStatus === 'نشط' ? 'تفعيل' : 'تعطيل';

                if (confirm(`هل أنت متأكد من ${action} المستخدم: ${user.name}؟`)) {
                    user.status = newStatus;
                    user.lastModified = new Date().toISOString().split('T')[0];

                    dataManager.saveData();
                    dataManager.logActivity(`تم ${action} المستخدم: ${user.name}`);

                    loadUsersTable();
                    loadDashboardData();
                    showSuccessNotification(`تم ${action} المستخدم "${user.name}" بنجاح`);
                }
            }
        }

        function deleteUser(id) {
            const user = usersData.find(u => u.id === id);
            if (user) {
                if (confirm(`⚠️ تحذير!\n\nهل أنت متأكد من حذف المستخدم: ${user.name}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`)) {
                    // حذف المستخدم
                    usersData = usersData.filter(u => u.id !== id);

                    dataManager.saveData();
                    dataManager.logActivity(`تم حذف المستخدم: ${user.name}`);

                    loadUsersTable();
                    loadDashboardData();
                    showSuccessNotification(`تم حذف المستخدم "${user.name}" بنجاح`);
                }
            }
        }

        // وظائف إضافة الوكلاء
        function showAddAgentModal() {
            document.getElementById('addAgentModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden';
        }

        function closeAddAgentModal() {
            document.getElementById('addAgentModal').classList.add('hidden');
            document.body.style.overflow = 'auto';
            document.getElementById('addAgentForm').reset();
            hideAddAgentMessage();
        }

        function updateCoordinates(city) {
            const citySelect = document.getElementById('addAgentCity');
            const selectedOption = citySelect.querySelector(`option[value="${city}"]`);

            if (selectedOption) {
                const lat = selectedOption.getAttribute('data-lat');
                const lng = selectedOption.getAttribute('data-lng');

                document.getElementById('addAgentLat').value = lat || '';
                document.getElementById('addAgentLng').value = lng || '';
            }
        }

        function handleAddAgent(event) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const newAgent = {
                id: Date.now(), // ID مؤقت
                name: formData.get('name').trim(),
                city: formData.get('city'),
                phone: formData.get('phone').trim(),
                address: formData.get('address').trim(),
                lat: parseFloat(formData.get('lat')),
                lng: parseFloat(formData.get('lng')),
                rating: parseFloat(formData.get('rating')),
                status: formData.get('status'),
                createdAt: new Date().toISOString().split('T')[0]
            };

            // التحقق من صحة البيانات
            if (!newAgent.name || !newAgent.city || !newAgent.phone || !newAgent.address) {
                showAddAgentMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من تنسيق رقم الهاتف
            const phoneRegex = /^\+218[0-9\s]{9,}$/;
            if (!phoneRegex.test(newAgent.phone)) {
                showAddAgentMessage('يرجى إدخال رقم هاتف ليبي صحيح', 'error');
                return;
            }

            // التحقق من عدم تكرار رقم الهاتف
            const existingAgent = agentsData.find(a => a.phone === newAgent.phone);
            if (existingAgent) {
                showAddAgentMessage('رقم الهاتف مستخدم من قبل وكيل آخر', 'error');
                return;
            }

            // التحقق من الحد الأقصى للوكلاء
            if (agentsData.length >= systemSettings.maxAgents) {
                showAddAgentMessage(`تم الوصول للحد الأقصى (${systemSettings.maxAgents} وكيل)`, 'error');
                return;
            }

            showAddAgentMessage('جاري إضافة الوكيل...', 'info');

            // محاكاة عملية الإضافة
            setTimeout(() => {
                agentsData.push(newAgent);

                // إضافة للحفظ التلقائي
                autoSaveManager.addToQueue({
                    description: `إضافة وكيل جديد: ${newAgent.name}`,
                    priority: 'high'
                });

                dataManager.logActivity(`تم إضافة وكيل جديد: ${newAgent.name} في ${newAgent.city}`);

                showAddAgentMessage('تم إضافة الوكيل بنجاح! 🎉', 'success');

                setTimeout(() => {
                    closeAddAgentModal();
                    loadAgentsTable();
                    loadDashboardData();
                    showSuccessNotification(`تم إضافة الوكيل "${newAgent.name}" بنجاح`);
                }, 1500);
            }, 1000);
        }

        function showAddAgentMessage(message, type = 'info') {
            const messageDiv = document.getElementById('addAgentMessage');
            messageDiv.className = `mt-4 p-3 rounded-lg text-sm text-center ${
                type === 'success' ? 'bg-green-100 text-green-800' :
                type === 'error' ? 'bg-red-100 text-red-800' :
                'bg-blue-100 text-blue-800'
            }`;
            messageDiv.textContent = message;
            messageDiv.classList.remove('hidden');
        }

        function hideAddAgentMessage() {
            document.getElementById('addAgentMessage').classList.add('hidden');
        }

        // وظائف إدارة النظام المتقدمة
        function saveSystemSettings() {
            // جمع جميع القيم من النموذج
            const siteName = document.getElementById('systemSiteName').value.trim();
            const siteDescription = document.getElementById('systemSiteDescription').value.trim();
            const maxAgents = parseInt(document.getElementById('systemMaxAgents').value);
            const maintenanceMode = document.getElementById('systemMaintenanceMode').checked;
            const autoBackup = document.getElementById('systemAutoBackup').checked;
            const emailNotifications = document.getElementById('systemEmailNotifications').checked;

            // التحقق من صحة البيانات
            if (!siteName) {
                alert('يرجى إدخال اسم الموقع');
                return;
            }

            if (!siteDescription) {
                alert('يرجى إدخال وصف الموقع');
                return;
            }

            if (isNaN(maxAgents) || maxAgents < 1 || maxAgents > 1000) {
                alert('يرجى إدخال حد أقصى صحيح للوكلاء (1-1000)');
                return;
            }

            // إظهار مؤشر التحميل
            const saveButton = event.target;
            const originalText = saveButton.innerHTML;
            saveButton.innerHTML = '⏳ جاري الحفظ...';
            saveButton.disabled = true;

            // محاكاة عملية الحفظ
            setTimeout(() => {
                // تحديث الإعدادات
                systemSettings.siteName = siteName;
                systemSettings.siteDescription = siteDescription;
                systemSettings.maxAgents = maxAgents;
                systemSettings.maintenanceMode = maintenanceMode;
                systemSettings.autoBackup = autoBackup;
                systemSettings.emailNotifications = emailNotifications;
                systemSettings.lastUpdate = new Date().toISOString();

                // حفظ البيانات
                const saved = dataManager.saveData();

                if (saved) {
                    dataManager.logActivity('تم حفظ إعدادات النظام بنجاح');
                    showSuccessNotification('تم حفظ جميع الإعدادات بنجاح! 🎉');

                    // تحديث الإحصائيات
                    updateSystemStats();
                } else {
                    alert('حدث خطأ أثناء حفظ الإعدادات. يرجى المحاولة مرة أخرى.');
                }

                // إعادة تعيين الزر
                saveButton.innerHTML = originalText;
                saveButton.disabled = false;
            }, 1500);
        }

        function updateSystemSetting(key, value) {
            systemSettings[key] = value;
            dataManager.saveData();
            dataManager.logActivity(`تم تحديث إعداد النظام: ${key} = ${value}`);
        }

        // نظام الحفظ التلقائي المتقدم
        class AutoSaveManager {
            constructor() {
                this.saveQueue = [];
                this.isProcessing = false;
                this.lastSave = Date.now();
                this.saveInterval = 30000; // 30 ثانية
                this.maxQueueSize = 10;

                // بدء المراقبة
                this.startAutoSave();
            }

            // إضافة عملية للطابور
            addToQueue(operation) {
                this.saveQueue.push({
                    operation,
                    timestamp: Date.now(),
                    id: Math.random().toString(36).substr(2, 9)
                });

                // تنظيف الطابور إذا امتلأ
                if (this.saveQueue.length > this.maxQueueSize) {
                    this.saveQueue = this.saveQueue.slice(-this.maxQueueSize);
                }

                // حفظ فوري إذا كان هناك تغييرات مهمة
                if (operation.priority === 'high') {
                    this.processSaveQueue();
                }
            }

            // معالجة طابور الحفظ
            async processSaveQueue() {
                if (this.isProcessing || this.saveQueue.length === 0) return;

                this.isProcessing = true;

                try {
                    // حفظ البيانات
                    const success = dataManager.saveData();

                    if (success) {
                        // تسجيل العمليات المحفوظة
                        const operations = this.saveQueue.map(item => item.operation.description).join(', ');
                        dataManager.logActivity(`حفظ تلقائي: ${operations}`);

                        // مسح الطابور
                        this.saveQueue = [];
                        this.lastSave = Date.now();

                        // إظهار مؤشر الحفظ
                        this.showAutoSaveIndicator();
                    }
                } catch (error) {
                    console.error('خطأ في الحفظ التلقائي:', error);
                } finally {
                    this.isProcessing = false;
                }
            }

            // بدء الحفظ التلقائي
            startAutoSave() {
                setInterval(() => {
                    if (this.saveQueue.length > 0) {
                        this.processSaveQueue();
                    }
                }, this.saveInterval);
            }

            // إظهار مؤشر الحفظ التلقائي
            showAutoSaveIndicator() {
                const indicator = document.getElementById('autoSaveIndicator') || this.createAutoSaveIndicator();
                indicator.className = 'fixed bottom-4 left-4 bg-green-100 border border-green-400 text-green-700 px-3 py-2 rounded-lg shadow-lg z-50 transition-all duration-300';
                indicator.innerHTML = '💾 تم الحفظ التلقائي';

                setTimeout(() => {
                    indicator.className = indicator.className.replace('bg-green-100 border-green-400 text-green-700', 'bg-gray-100 border-gray-300 text-gray-500');
                    indicator.innerHTML = '💾 حفظ تلقائي';
                }, 3000);
            }

            // إنشاء مؤشر الحفظ التلقائي
            createAutoSaveIndicator() {
                const indicator = document.createElement('div');
                indicator.id = 'autoSaveIndicator';
                indicator.className = 'fixed bottom-4 left-4 bg-gray-100 border border-gray-300 text-gray-500 px-3 py-2 rounded-lg shadow-lg z-50';
                indicator.innerHTML = '💾 حفظ تلقائي';
                document.body.appendChild(indicator);
                return indicator;
            }

            // حفظ فوري
            forceSave() {
                this.processSaveQueue();
            }
        }

        // إنشاء مثيل من مدير الحفظ التلقائي
        const autoSaveManager = new AutoSaveManager();

        function updateAdminPassword() {
            const newPassword = document.getElementById('systemAdminPassword').value;

            if (!newPassword || newPassword.length < 6) {
                alert('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return;
            }

            if (confirm('هل أنت متأكد من تغيير كلمة مرور المدير؟')) {
                systemSettings.adminPassword = newPassword;
                dataManager.saveData();
                dataManager.logActivity('تم تحديث كلمة مرور المدير');
                document.getElementById('systemAdminPassword').value = '';
                showSuccessNotification('تم تحديث كلمة المرور بنجاح');
            }
        }

        function loadSystemSettings() {
            document.getElementById('systemSiteName').value = systemSettings.siteName;
            document.getElementById('systemSiteDescription').value = systemSettings.siteDescription;
            document.getElementById('systemMaxAgents').value = systemSettings.maxAgents;
            document.getElementById('systemMaintenanceMode').checked = systemSettings.maintenanceMode;
            document.getElementById('systemAutoBackup').checked = systemSettings.autoBackup;
            document.getElementById('systemEmailNotifications').checked = systemSettings.emailNotifications;

            updateSystemStats();
        }

        function updateSystemStats() {
            // حساب حجم البيانات
            const dataSize = new Blob([localStorage.getItem('alwatan_admin_data') || '']).size;
            document.getElementById('dataSize').textContent = formatBytes(dataSize);

            // عدد الأنشطة
            const activities = JSON.parse(localStorage.getItem('alwatan_activities') || '[]');
            document.getElementById('activitiesCount').textContent = activities.length;

            // آخر نسخة احتياطية
            const lastBackup = localStorage.getItem('alwatan_last_backup');
            document.getElementById('lastBackup').textContent = lastBackup ?
                new Date(lastBackup).toLocaleString('ar-LY') : 'لم يتم إنشاء نسخة';

            // وقت التشغيل
            const startTime = sessionStorage.getItem('app_start_time') || Date.now();
            const uptime = Date.now() - parseInt(startTime);
            document.getElementById('uptime').textContent = formatUptime(uptime);
        }

        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function formatUptime(ms) {
            const seconds = Math.floor(ms / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 0) return `${days} يوم، ${hours % 24} ساعة`;
            if (hours > 0) return `${hours} ساعة، ${minutes % 60} دقيقة`;
            if (minutes > 0) return `${minutes} دقيقة، ${seconds % 60} ثانية`;
            return `${seconds} ثانية`;
        }

        // وظائف النسخ الاحتياطي
        function exportAllData() {
            dataManager.exportData();
            localStorage.setItem('alwatan_last_backup', new Date().toISOString());
            updateSystemStats();
        }

        function exportAgentsOnly() {
            const data = { agents: agentsData, exportDate: new Date().toISOString() };
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `agents-backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            dataManager.logActivity('تم تصدير بيانات الوكلاء');
        }

        function exportUsersOnly() {
            const data = { users: usersData, exportDate: new Date().toISOString() };
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `users-backup-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            dataManager.logActivity('تم تصدير بيانات المستخدمين');
        }

        function handleFileImport(file) {
            if (!file) return;

            if (confirm('هل أنت متأكد؟ سيتم استبدال جميع البيانات الحالية!')) {
                dataManager.importData(file)
                    .then(() => {
                        showSuccessNotification('تم استيراد البيانات بنجاح');
                        loadDashboardData();
                        loadAgentsTable();
                        loadUsersTable();
                        loadSystemSettings();
                    })
                    .catch(error => {
                        alert(`خطأ في استيراد البيانات: ${error}`);
                    });
            }
        }

        function showAddUserModal() {
            alert('نافذة إضافة مستخدم جديد\n\nهذه الميزة قيد التطوير...');
        }

        function handleLogin(event) {
            event.preventDefault();

            const phone = document.getElementById('phone').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;

            // التحقق من صحة البيانات
            if (!phone || !password) {
                showLoginMessage('يرجى ملء جميع الحقول', 'error');
                return;
            }

            // التحقق من تنسيق رقم الهاتف الليبي
            const phoneRegex = /^\+218[0-9]{9}$/;
            if (!phoneRegex.test(phone)) {
                showLoginMessage('يرجى إدخال رقم هاتف ليبي صحيح', 'error');
                return;
            }

            // محاكاة عملية تسجيل الدخول
            showLoginMessage('جاري تسجيل الدخول...', 'info');

            setTimeout(() => {
                // حسابات تجريبية
                const validAccounts = {
                    '+************': { password: '123456', name: 'أحمد محمد', type: 'user' },
                    '+************': { password: '123456', name: 'فاطمة علي', type: 'agent' },
                    '+************': { password: 'admin123', name: 'مدير النظام', type: 'admin' }
                };

                if (validAccounts[phone] && validAccounts[phone].password === password) {
                    // نجح تسجيل الدخول
                    currentUser = {
                        phone: phone,
                        name: validAccounts[phone].name,
                        type: validAccounts[phone].type
                    };
                    isLoggedIn = true;

                    if (remember) {
                        localStorage.setItem('rememberedUser', JSON.stringify(currentUser));
                    }

                    showLoginMessage('تم تسجيل الدخول بنجاح! 🎉', 'success');

                    setTimeout(() => {
                        closeLoginModal();
                        updateUIForLoggedInUser();
                        showWelcomeMessage();
                    }, 1500);
                } else {
                    showLoginMessage('رقم الهاتف أو كلمة المرور غير صحيحة', 'error');
                }
            }, 1000);
        }

        function quickLogin(type) {
            if (type === 'demo') {
                document.getElementById('phone').value = '+************';
                document.getElementById('password').value = '123456';
                showLoginMessage('تم ملء البيانات التجريبية', 'info');
            } else if (type === 'guest') {
                currentUser = { name: 'زائر', type: 'guest' };
                isLoggedIn = true;
                closeLoginModal();
                updateUIForLoggedInUser();
                showWelcomeMessage();
            }
        }

        function updateUIForLoggedInUser() {
            const loginBtn = document.getElementById('loginBtn');
            if (loginBtn && currentUser) {
                loginBtn.innerHTML = `
                    <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    ${currentUser.name}
                `;
                loginBtn.onclick = showUserMenu;
                loginBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                loginBtn.classList.add('bg-green-600', 'hover:bg-green-700');
            }
        }

        function showWelcomeMessage() {
            if (currentUser) {
                const welcomeDiv = document.createElement('div');
                welcomeDiv.className = 'fixed top-4 right-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg shadow-lg z-50';
                welcomeDiv.innerHTML = `
                    <div class="flex items-center">
                        <span class="text-lg ml-2">👋</span>
                        <span>مرحباً ${currentUser.name}!</span>
                    </div>
                `;
                document.body.appendChild(welcomeDiv);

                setTimeout(() => {
                    welcomeDiv.remove();
                }, 3000);
            }
        }

        function showUserMenu() {
            // يمكن إضافة قائمة المستخدم هنا
            alert(`مرحباً ${currentUser.name}!\nنوع الحساب: ${currentUser.type}\n\nالمميزات قريباً...`);
        }

        function showForgotPassword() {
            alert('سيتم إرسال رابط إعادة تعيين كلمة المرور إلى رقم هاتفك المسجل.\n\nهذه الميزة قيد التطوير...');
        }

        function showRegisterForm() {
            alert('صفحة التسجيل قيد التطوير...\n\nيمكنك استخدام الحساب التجريبي:\nالهاتف: +************\nكلمة المرور: 123456');
        }

        // التحقق من وجود مستخدم محفوظ عند تحميل الصفحة
        function checkRememberedUser() {
            const rememberedUser = localStorage.getItem('rememberedUser');
            if (rememberedUser) {
                currentUser = JSON.parse(rememberedUser);
                isLoggedIn = true;
                updateUIForLoggedInUser();
            }
        }

        // وظائف التفاعل مع الهواتف
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }

        function scrollToSection(sectionId) {
            const element = document.getElementById(sectionId);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth' });
                // إغلاق القائمة بعد النقر
                document.getElementById('mobileMenu').classList.add('hidden');
            }
        }

        function toggleFullscreenMap() {
            const mapContainer = document.getElementById('mapContainer');
            const closeButton = document.getElementById('closeFullscreenMap');

            mapContainer.classList.add('fixed', 'inset-0', 'z-50', 'bg-white');
            mapContainer.style.height = '100vh';
            closeButton.classList.remove('hidden');

            // إعادة تحجيم الخريطة
            if (map) {
                setTimeout(() => {
                    google.maps.event.trigger(map, 'resize');
                }, 300);
            }
        }

        function closeFullscreenMap() {
            const mapContainer = document.getElementById('mapContainer');
            const closeButton = document.getElementById('closeFullscreenMap');

            mapContainer.classList.remove('fixed', 'inset-0', 'z-50', 'bg-white');
            mapContainer.style.height = '';
            closeButton.classList.add('hidden');

            // إعادة تحجيم الخريطة
            if (map) {
                setTimeout(() => {
                    google.maps.event.trigger(map, 'resize');
                }, 300);
            }
        }

        // وظائف جديدة للخريطة العصرية
        function refreshMap() {
            if (map) {
                // إظهار مؤشر التحميل
                const loader = document.getElementById('mapLoader');
                if (loader) {
                    loader.style.display = 'flex';
                    setTimeout(() => {
                        loader.style.display = 'none';
                        showSuccessNotification('تم تحديث الخريطة بنجاح');
                    }, 1500);
                }

                // إعادة تحميل العلامات
                clearMarkers();
                addAgentMarkers();

                // تحديث الإحصائيات
                updateMapStats();
            }
        }

        function updateMapStats() {
            const agentsCount = agentsData.length;
            const citiesCount = [...new Set(agentsData.map(agent => agent.city))].length;

            const agentsCountEl = document.getElementById('mapAgentsCount');
            const citiesCountEl = document.getElementById('mapCitiesCount');

            if (agentsCountEl) agentsCountEl.textContent = agentsCount;
            if (citiesCountEl) citiesCountEl.textContent = citiesCount;
        }

        function clearMarkers() {
            markers.forEach(marker => marker.setMap(null));
            markers = [];
        }

        // وظائف المكتب الرئيسي
        function handleImageClick() {
            if (isAdminLoggedIn) {
                triggerImageUpload();
            } else {
                showAdminRequiredMessage();
            }
        }

        function handleImageClickMobile() {
            if (isAdminLoggedIn) {
                triggerImageUpload();
            } else {
                showAdminRequiredMessage();
            }
        }

        function triggerImageUpload() {
            if (!isAdminLoggedIn) {
                showAdminRequiredMessage();
                return;
            }
            document.getElementById('imageUpload').click();
        }

        function showAdminRequiredMessage() {
            alert('🔒 تنبيه أمني\n\nيمكن للمدير فقط تعديل صورة المكتب الرئيسي.\n\nيرجى تسجيل الدخول كمدير أولاً.');
        }

        function handleImageUpload(event) {
            if (!isAdminLoggedIn) {
                showAdminRequiredMessage();
                return;
            }

            const file = event.target.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            if (!file.type.startsWith('image/')) {
                alert('يرجى اختيار ملف صورة صحيح (JPG, PNG, GIF)');
                return;
            }

            // التحقق من حجم الملف (5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5MB');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                // للشاشات الكبيرة
                const imageContainer = document.querySelector('.office-image-container');
                const officeImage = document.getElementById('officeImage');
                const placeholder = document.getElementById('imagePlaceholder');

                if (officeImage && placeholder && imageContainer) {
                    officeImage.src = e.target.result;
                    officeImage.classList.remove('hidden');
                    placeholder.classList.add('hidden');
                    imageContainer.classList.add('has-image');
                }

                // للهواتف المحمولة
                const officeImageMobile = document.getElementById('officeImageMobile');
                const placeholderMobile = document.getElementById('imagePlaceholderMobile');

                if (officeImageMobile && placeholderMobile) {
                    officeImageMobile.src = e.target.result;
                    officeImageMobile.classList.remove('hidden');
                    placeholderMobile.classList.add('hidden');
                }

                // حفظ الصورة في localStorage
                localStorage.setItem('office_image', e.target.result);

                // إظهار رسالة نجاح
                showSuccessNotification('تم رفع صورة المكتب بنجاح! 📸');

                // تسجيل النشاط
                if (typeof dataManager !== 'undefined') {
                    dataManager.logActivity('تم رفع صورة جديدة للمكتب الرئيسي');
                }
            };

            reader.readAsDataURL(file);
        }

        function showOfficeDetails() {
            const details = `
🏛️ المكتب الرئيسي - نقطة قريبة

📍 العنوان: شارع عمر المختار، وسط البلد، طرابلس
📞 الهاتف: +218 21 234 5678
💬 واتساب: +218 91 123 4567
📧 البريد: <EMAIL>

🕒 ساعات العمل:
   السبت - الخميس: 8:00 ص - 6:00 م
   الجمعة: مغلق

⭐ التقييم: 4.9/5 (324 تقييم)

🏢 الخدمات المتاحة:
   • استقبال العملاء
   • الاستشارات المجانية
   • خدمة العملاء
   • الدعم التقني
   • إدارة الحسابات

🚗 مواقف السيارات متاحة
♿ مناسب لذوي الاحتياجات الخاصة
🌐 واي فاي مجاني
            `;

            alert(details);
        }

        function removeOfficeImage() {
            if (!isAdminLoggedIn) {
                showAdminRequiredMessage();
                return;
            }

            if (confirm('هل أنت متأكد من حذف صورة المكتب الرئيسي؟')) {
                // إخفاء الصورة وإظهار المكان الفارغ
                const officeImage = document.getElementById('officeImage');
                const placeholder = document.getElementById('imagePlaceholder');
                const officeImageMobile = document.getElementById('officeImageMobile');
                const placeholderMobile = document.getElementById('imagePlaceholderMobile');

                // للشاشات الكبيرة
                if (officeImage && placeholder) {
                    officeImage.classList.add('hidden');
                    placeholder.classList.remove('hidden');
                    officeImage.src = '';
                }

                // للهواتف
                if (officeImageMobile && placeholderMobile) {
                    officeImageMobile.classList.add('hidden');
                    placeholderMobile.classList.remove('hidden');
                    officeImageMobile.src = '';
                }

                // إزالة الصورة من التخزين المحلي
                localStorage.removeItem('office_image');

                // إزالة فئة has-image
                const imageContainers = document.querySelectorAll('.office-image-container');
                imageContainers.forEach(container => {
                    container.classList.remove('has-image');
                });

                showSuccessNotification('تم حذف صورة المكتب بنجاح');

                if (typeof dataManager !== 'undefined') {
                    dataManager.logActivity('تم حذف صورة المكتب الرئيسي');
                }
            }
        }

        // تحميل صورة المكتب المحفوظة عند تحميل الصفحة
        function loadSavedOfficeImage() {
            const savedImage = localStorage.getItem('office_image');
            if (savedImage) {
                // للشاشات الكبيرة
                const imageContainer = document.querySelector('.office-image-container');
                const officeImage = document.getElementById('officeImage');
                const placeholder = document.getElementById('imagePlaceholder');

                if (officeImage && placeholder && imageContainer) {
                    officeImage.src = savedImage;
                    officeImage.classList.remove('hidden');
                    placeholder.classList.add('hidden');
                    imageContainer.classList.add('has-image');
                }

                // للهواتف المحمولة
                const officeImageMobile = document.getElementById('officeImageMobile');
                const placeholderMobile = document.getElementById('imagePlaceholderMobile');

                if (officeImageMobile && placeholderMobile) {
                    officeImageMobile.src = savedImage;
                    officeImageMobile.classList.remove('hidden');
                    placeholderMobile.classList.add('hidden');
                }
            }
        }

        // تحسين الأداء للهواتف
        function optimizeForMobile() {
            // تحسين الخريطة للهواتف
            if (window.innerWidth < 768 && map) {
                map.setOptions({
                    gestureHandling: 'cooperative',
                    zoomControl: true,
                    mapTypeControl: false,
                    scaleControl: false,
                    streetViewControl: false,
                    rotateControl: false,
                    fullscreenControl: false
                });
            }
        }

        // معالج النقر على زر القائمة
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.getElementById('menuToggle');
            if (menuToggle) {
                menuToggle.addEventListener('click', toggleMobileMenu);
            }

            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(event) {
                const menu = document.getElementById('mobileMenu');
                const toggle = document.getElementById('menuToggle');
                const adminModal = document.getElementById('adminLoginModal');
                const editModal = document.getElementById('editAgentModal');
                const deleteModal = document.getElementById('deleteConfirmModal');
                const addModal = document.getElementById('addAgentModal');

                if (menu && !menu.contains(event.target) && !toggle.contains(event.target)) {
                    menu.classList.add('hidden');
                }

                // إغلاق نافذة تسجيل دخول المدير عند النقر خارجها
                if (adminModal && event.target === adminModal) {
                    closeAdminLogin();
                }

                // إغلاق نافذة تعديل الوكيل عند النقر خارجها
                if (editModal && event.target === editModal) {
                    closeEditAgentModal();
                }

                // إغلاق نافذة تأكيد الحذف عند النقر خارجها
                if (deleteModal && event.target === deleteModal) {
                    closeDeleteConfirmModal();
                }

                // إغلاق نافذة إضافة الوكيل عند النقر خارجها
                if (addModal && event.target === addModal) {
                    closeAddAgentModal();
                }
            });

            // إغلاق النوافذ بالضغط على Escape
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    const adminModal = document.getElementById('adminLoginModal');
                    const dashboard = document.getElementById('adminDashboard');
                    const editModal = document.getElementById('editAgentModal');
                    const deleteModal = document.getElementById('deleteConfirmModal');
                    const addModal = document.getElementById('addAgentModal');

                    if (addModal && !addModal.classList.contains('hidden')) {
                        closeAddAgentModal();
                    } else if (editModal && !editModal.classList.contains('hidden')) {
                        closeEditAgentModal();
                    } else if (deleteModal && !deleteModal.classList.contains('hidden')) {
                        closeDeleteConfirmModal();
                    } else if (adminModal && !adminModal.classList.contains('hidden')) {
                        closeAdminLogin();
                    } else if (dashboard && !dashboard.classList.contains('hidden')) {
                        if (confirm('هل تريد إغلاق لوحة التحكم؟')) {
                            logoutAdmin();
                        }
                    }
                }
            });

            // تحسين الأداء عند تغيير حجم الشاشة
            window.addEventListener('resize', optimizeForMobile);
        });

        // تهيئة الخريطة عند تحميل الصفحة
        window.onload = function() {
            console.log('🇱🇾 نقطة قريبة - ليبيا');
            console.log('✅ التطبيق يعمل بنجاح!');

            // تسجيل وقت بدء التشغيل
            if (!sessionStorage.getItem('app_start_time')) {
                sessionStorage.setItem('app_start_time', Date.now().toString());
            }

            // تهيئة مدير البيانات
            dataManager.createSaveIndicator();
            dataManager.logActivity('تم تشغيل النظام');

            // تحميل صورة المكتب المحفوظة
            setTimeout(() => {
                loadSavedOfficeImage();
            }, 1000);

            if (typeof google !== 'undefined') {
                initMap();
                optimizeForMobile();
            } else {
                console.log('⏳ انتظار تحميل Google Maps API...');
                setTimeout(() => {
                    if (typeof google !== 'undefined') {
                        initMap();
                        optimizeForMobile();
                    } else {
                        console.log('❌ فشل في تحميل Google Maps API');
                        const mapElement = document.getElementById('mapContainer');
                        if (mapElement) {
                            mapElement.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f3f4f6; color: #666; border-radius: 12px;">⚠️ تعذر تحميل الخريطة</div>';
                        }
                    }
                }, 3000);
            }
        };
    </script>
</body>
</html>
